import { useState, useCallback } from 'react';
import { soundApi, SoundGenerationRequest, SoundGenerationResponse, SoundTask, SoundType, validateSoundRequest } from '@/lib/api/sound';
import { useAuth } from '@/contexts/auth-context';
import { useToast } from '@/hooks/use-sonner-toast';
import { useSoundStore } from '@/store/useSoundStore';

// Sound Effect固定model_id
const SOUND_EFFECT_MODEL_ID = '6695239a-b5b9-4682-b343-4d889b6d5a50';

export interface SoundGenerationSettings {
    // Text-to-Speech Settings
    speed?: number;
    stability?: number;
    similarity_boost?: number;
    style_exaggeration?: number;
    timestamps?: boolean;

    // Sound Effect Settings
    duration_seconds?: number;
    prompt_influence?: number;
}

interface UseSoundGenerationState {
    isGenerating: boolean;
    generationProgress: number;
    currentTask: SoundTask | null;
    error: string | null;
}

export const useSoundGeneration = () => {
    const { user } = useAuth();
    const { success, error: toastError, info } = useToast();
    const { addToHistory } = useSoundStore();
    const [state, setState] = useState<UseSoundGenerationState>({
        isGenerating: false,
        generationProgress: 0,
        currentTask: null,
        error: null,
    });

    const generateSound = useCallback(async (
        text: string,
        soundType: SoundType,
        modelId?: string,
        voiceId?: string,
        settings?: SoundGenerationSettings
    ): Promise<void> => {
        if (!user) {
            toastError('Please log in first.');
            return;
        }

        const request: SoundGenerationRequest = {
            text,
            sound_type: soundType,
            model_id: modelId,
            voice_id: voiceId,
            settings: settings || {},
        };

        const validationError = validateSoundRequest(request);
        if (validationError) {
            setState(prev => ({ ...prev, error: validationError }));
            toastError(validationError);
            return;
        }

        if (soundType === SoundType.TEXT_TO_SPEECH) {
            if (!modelId) {
                const error = 'A TTS model must be selected for text-to-speech.';
                setState(prev => ({ ...prev, error }));
                toastError(error);
                return;
            }
            if (!voiceId) {
                const error = 'A voice must be selected for text-to-speech.';
                setState(prev => ({ ...prev, error }));
                toastError(error);
                return;
            }
        }

        if (soundType === SoundType.SOUND_EFFECT) {
            // Sound Effect模式固定使用指定的model_id
            request.model_id = SOUND_EFFECT_MODEL_ID;
        }

        setState(prev => ({
            ...prev,
            isGenerating: true,
            generationProgress: 0,
            error: null,
            currentTask: null,
        }));

        try {
            const response = await soundApi.generateSound(request);
            const taskId = response.task_id;

            if (!taskId) {
                throw new Error('No task ID received from server.');
            }

            // 立即获取任务详情并添加到history
            try {
                const newTask = await soundApi.getTaskStatus(taskId);
                // 立即将新任务添加到history列表顶部
                addToHistory(newTask);
            } catch (taskError) {
                console.warn('Failed to fetch new task details:', taskError);
                // 任务仍会通过后续的polling获取状态并更新history
            }

            // Start polling in the background without blocking the UI
            pollTaskStatus(taskId);

            // Reset button state immediately and notify user
            setState(prev => ({
                ...prev,
                isGenerating: false,
            }));
            success('Sound generation task started!');

        } catch (error: any) {
            console.error('Failed to generate sound:', error);
            const errorMessage = error?.response?.data?.message || 'Generation failed.';
            setState(prev => ({
                ...prev,
                isGenerating: false,
                error: errorMessage,
            }));
            toastError(errorMessage);
        }
    }, [user, addToHistory]);

    const pollTaskStatus = useCallback(async (taskId: string): Promise<void> => {
        const maxAttempts = 60; // Poll for 5 minutes max
        let attempts = 0;

        const poll = async (): Promise<void> => {
            attempts++;
            if (attempts > maxAttempts) {
                console.warn(`Max polling attempts reached for task ${taskId}.`);
                // Optionally notify user about timeout
                // toast.warn('A task took too long to complete.');
                return;
            }

            try {
                const task = await soundApi.getTaskStatus(taskId);
                const status = task.status;

                // 更新history中的任务状态
                addToHistory(task);

                switch (status) {
                    case 'completed':
                        console.log(`Task ${taskId} completed successfully.`);
                        success('A sound generation task has completed!');
                        return; // Stop polling

                    case 'failed':
                        const errorMessage = task.error_log?.message || 'A task has failed.';
                        console.error(`Task ${taskId} failed:`, errorMessage);
                        toastError(errorMessage);
                        return; // Stop polling

                    case 'pending':
                    case 'processing':
                        setTimeout(poll, 5000); // Continue polling
                        return;

                    default:
                        console.warn(`Unknown task status: ${status} for task ${taskId}.`);
                        setTimeout(poll, 5000); // Continue polling for unknown status
                        return;
                }
            } catch (error: any) {
                console.error(`Polling attempt ${attempts} for task ${taskId} failed:`, error);
                // Retry on API failure
                setTimeout(poll, 5000);
            }
        };

        await poll();
    }, [addToHistory]);

    const resetState = useCallback(() => {
        setState({
            isGenerating: false,
            generationProgress: 0,
            currentTask: null,
            error: null,
        });
    }, []);

    const cancelGeneration = useCallback(() => {
        // TODO: Implement cancellation logic on the server
        setState(prev => ({
            ...prev,
            isGenerating: false,
            error: 'Generation cancelled.',
        }));
        info('Generation cancelled.');
    }, []);

    return {
        ...state,
        generateSound,
        resetState,
        cancelGeneration,
    };
};
