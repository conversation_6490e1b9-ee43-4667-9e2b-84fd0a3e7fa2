'use client';

import React, { useState } from 'react';
import { AdminLayout } from '../blog/AdminLayout';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-sonner-toast';
import { Coins, User, Gift, Search } from 'lucide-react';
import { adminApi } from '@/lib/api/admin';

interface UserSearchResult {
    id: string;
    email: string;
    nickname: string;
    credits: number;
}

export default function CreditsManagementPage() {
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedUser, setSelectedUser] = useState<UserSearchResult | null>(null);
    const [amount, setAmount] = useState('');
    const [description, setDescription] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [isSearching, setIsSearching] = useState(false);
    const [searchResults, setSearchResults] = useState<UserSearchResult[]>([]);
    const [directUserId, setDirectUserId] = useState('');
    const [useDirectId, setUseDirectId] = useState(false);
    const { success, error } = useToast();

    // 搜索用户
    const handleSearchUsers = async () => {
        if (!searchQuery.trim()) {
            error('请输入搜索关键词');
            return;
        }

        setIsSearching(true);
        try {
            const results = await adminApi.searchUsers(searchQuery);
            setSearchResults(results);
            if (results.length === 0) {
                error('未找到匹配的用户');
            }
        } catch (err) {
            console.error('搜索用户失败:', err);
            error('搜索用户失败');
        } finally {
            setIsSearching(false);
        }
    };

    // 选择用户
    const handleSelectUser = (user: UserSearchResult) => {
        setSelectedUser(user);
        setSearchResults([]);
        setSearchQuery('');
    };

    // 重置选择
    const handleResetSelection = () => {
        setSelectedUser(null);
        setSearchResults([]);
        setSearchQuery('');
        setDirectUserId('');
        setAmount('');
        setDescription('');
    };

    // 发放积分
    const handleGrantCredits = async () => {
        const userId = useDirectId ? directUserId : selectedUser?.id;

        if (!userId) {
            error('请选择用户或输入用户ID');
            return;
        }

        if (!amount || isNaN(Number(amount)) || Number(amount) <= 0) {
            error('请输入有效的积分数量');
            return;
        }

        if (!description.trim()) {
            error('请输入发放说明');
            return;
        }

        setIsLoading(true);
        try {
            await adminApi.grantCredits({
                userId,
                amount: Number(amount),
                description: description.trim()
            });

            success('积分发放成功');
            handleResetSelection();
        } catch (err) {
            console.error('积分发放失败:', err);
            error('积分发放失败');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <AdminLayout>
            <div className="space-y-8">
                {/* 页面标题 */}
                <div className="text-center">
                    <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                        Credits Management
                    </h1>
                    <p className="text-lg text-gray-600 dark:text-gray-300">
                        Grant credits to users manually
                    </p>
                </div>

                {/* 发放积分表单 */}
                <div className="max-w-2xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <div className="flex items-center mb-6">
                        <Coins className="w-6 h-6 text-yellow-500 mr-3" />
                        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                            Grant Credits
                        </h2>
                    </div>

                    {/* 用户选择模式切换 */}
                    <div className="mb-6">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            User Selection Mode
                        </label>
                        <div className="flex space-x-4">
                            <label className="flex items-center">
                                <input
                                    type="radio"
                                    checked={!useDirectId}
                                    onChange={() => setUseDirectId(false)}
                                    className="mr-2"
                                />
                                <span className="text-sm text-gray-700 dark:text-gray-300">Search by Email/Nickname</span>
                            </label>
                            <label className="flex items-center">
                                <input
                                    type="radio"
                                    checked={useDirectId}
                                    onChange={() => setUseDirectId(true)}
                                    className="mr-2"
                                />
                                <span className="text-sm text-gray-700 dark:text-gray-300">Direct User ID</span>
                            </label>
                        </div>
                    </div>

                    {!useDirectId ? (
                        <>
                            {/* 用户搜索 */}
                            <div className="mb-6">
                                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                    Search User
                                </label>
                                <div className="flex space-x-2">
                                    <div className="flex-1 relative">
                                        <Input
                                            type="text"
                                            value={searchQuery}
                                            onChange={(e) => setSearchQuery(e.target.value)}
                                            placeholder="Enter email or nickname..."
                                            className="pl-10"
                                            onKeyPress={(e) => e.key === 'Enter' && handleSearchUsers()}
                                        />
                                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                                    </div>
                                    <Button
                                        onClick={handleSearchUsers}
                                        disabled={isSearching || !searchQuery.trim()}
                                        className="whitespace-nowrap"
                                    >
                                        {isSearching ? 'Searching...' : 'Search'}
                                    </Button>
                                </div>
                            </div>

                            {/* 搜索结果 */}
                            {searchResults.length > 0 && (
                                <div className="mb-6">
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Search Results
                                    </label>
                                    <div className="space-y-2 max-h-40 overflow-y-auto">
                                        {searchResults.map((user) => (
                                            <div
                                                key={user.id}
                                                onClick={() => handleSelectUser(user)}
                                                className="p-3 border border-gray-200 dark:border-gray-600 rounded-md cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                                            >
                                                <div className="flex items-center justify-between">
                                                    <div>
                                                        <p className="font-medium text-gray-900 dark:text-white">
                                                            {user.nickname}
                                                        </p>
                                                        <p className="text-sm text-gray-500 dark:text-gray-400">
                                                            {user.email}
                                                        </p>
                                                    </div>
                                                    <div className="text-right">
                                                        <p className="text-sm text-gray-600 dark:text-gray-300">
                                                            Current Credits
                                                        </p>
                                                        <p className="font-semibold text-yellow-600 dark:text-yellow-400">
                                                            {user.credits}
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}

                            {/* 已选择的用户 */}
                            {selectedUser && (
                                <div className="mb-6">
                                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                        Selected User
                                    </label>
                                    <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-md border border-gray-200 dark:border-gray-600">
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center">
                                                <User className="w-5 h-5 text-gray-500 mr-2" />
                                                <div>
                                                    <p className="font-medium text-gray-900 dark:text-white">
                                                        {selectedUser.nickname}
                                                    </p>
                                                    <p className="text-sm text-gray-500 dark:text-gray-400">
                                                        {selectedUser.email}
                                                    </p>
                                                    <p className="text-xs text-gray-400 dark:text-gray-500">
                                                        ID: {selectedUser.id}
                                                    </p>
                                                </div>
                                            </div>
                                            <div className="text-right">
                                                <p className="text-sm text-gray-600 dark:text-gray-300">
                                                    Current Credits
                                                </p>
                                                <p className="font-semibold text-yellow-600 dark:text-yellow-400">
                                                    {selectedUser.credits}
                                                </p>
                                            </div>
                                        </div>
                                        <Button
                                            onClick={handleResetSelection}
                                            variant="outline"
                                            size="sm"
                                            className="mt-3"
                                        >
                                            Change User
                                        </Button>
                                    </div>
                                </div>
                            )}
                        </>
                    ) : (
                        /* 直接输入用户ID */
                        <div className="mb-6">
                            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                User ID
                            </label>
                            <Input
                                type="text"
                                value={directUserId}
                                onChange={(e) => setDirectUserId(e.target.value)}
                                placeholder="Enter user ID (UUID format)..."
                                className="font-mono"
                            />
                        </div>
                    )}

                    {/* 积分数量 */}
                    <div className="mb-6">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Credits Amount
                        </label>
                        <Input
                            type="number"
                            value={amount}
                            onChange={(e) => setAmount(e.target.value)}
                            placeholder="Enter credits amount..."
                            min="1"
                            step="1"
                        />
                    </div>

                    {/* 发放说明 */}
                    <div className="mb-6">
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Description
                        </label>
                        <Textarea
                            value={description}
                            onChange={(e) => setDescription(e.target.value)}
                            placeholder="Enter reason for granting credits..."
                            rows={3}
                        />
                    </div>

                    {/* 提交按钮 */}
                    <div className="flex justify-end space-x-3">
                        <Button
                            onClick={handleResetSelection}
                            variant="outline"
                            disabled={isLoading}
                        >
                            Reset
                        </Button>
                        <Button
                            onClick={handleGrantCredits}
                            disabled={isLoading || (!selectedUser && !directUserId) || !amount || !description.trim()}
                            className="bg-yellow-600 hover:bg-yellow-700 text-white"
                        >
                            <Gift className="w-4 h-4 mr-2" />
                            {isLoading ? 'Granting...' : 'Grant Credits'}
                        </Button>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
} 