"use client";

import React, { useState, ChangeEvent, useEffect, useCallback } from "react";
import { Wand2, InfoIcon, Undo2, AlertCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { generationApi } from "@/lib/api/generation";
import { useToast } from "@/hooks/use-sonner-toast";
import { useAuthProtectedCallback } from "@/components/auth/with-auth";

const maxLength = 1000;

interface PromptInputProps {
    initialPrompt: string;
    onPromptChange: (newPrompt: string) => void; // Debounced update function for global state
    errorMessage?: string | null;
}

const PromptInputComponent: React.FC<PromptInputProps> = ({
    initialPrompt,
    onPromptChange,
    errorMessage,
}) => {
    const [localPrompt, setLocalPrompt] = useState(initialPrompt);
    const [isEnhancingPrompt, setIsEnhancingPrompt] = useState(false);
    const [showEnhanceTip, setShowEnhanceTip] = useState(false);
    const [originalPrompt, setOriginalPrompt] = useState<string | null>(null);
    const [showUndoButton, setShowUndoButton] = useState(false);
    const [isMobile, setIsMobile] = useState(false);
    const [screenWidth, setScreenWidth] = useState<number>(typeof window !== 'undefined' ? window.innerWidth : 1024);

    const { success, error: toastError } = useToast();

    // Check if device is mobile
    useEffect(() => {
        const checkIfMobile = () => {
            const width = window.innerWidth;
            setScreenWidth(width);
            setIsMobile(width < 768);
        };

        // Set initial value
        checkIfMobile();

        // Add event listener for window resize
        window.addEventListener('resize', checkIfMobile);

        // Clean up
        return () => window.removeEventListener('resize', checkIfMobile);
    }, []);

    // Sync local prompt state when the initialPrompt prop changes from parent
    useEffect(() => {
        setLocalPrompt(initialPrompt);
    }, [initialPrompt]);

    // Handle local prompt input changes
    const handleLocalPromptChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
        const newValue = e.target.value;
        setLocalPrompt(newValue); // Update local state immediately for responsiveness
        onPromptChange(newValue); // Call the debounced update function passed from parent
    };

    // Initialize enhance tip state on mount
    useEffect(() => {
        const tipDismissed = localStorage.getItem('enhanceTipDismissed') === 'true';
        if (!tipDismissed && !isMobile) {
            setShowEnhanceTip(true);
        }
    }, [isMobile]);

    // Handle Enhance Prompt
    const handleEnhancePrompt = useCallback(async () => {
        setShowEnhanceTip(false); // Close tip first
        if (!localPrompt.trim() || isEnhancingPrompt) return;

        try {
            setOriginalPrompt(localPrompt); // Save current local prompt
            setIsEnhancingPrompt(true);
            const result = await generationApi.enhancePrompt(localPrompt);

            if (result && result.enhancedPrompt) {
                setLocalPrompt(result.enhancedPrompt); // Update local state
                onPromptChange(result.enhancedPrompt); // Update global state via debounced prop
                setShowUndoButton(true);
                success("Prompt enhanced successfully!");
            }
        } catch (error) {
            console.error('Enhance prompt error:', error);
            toastError(error instanceof Error ? error.message : 'Failed to enhance prompt');
        } finally {
            setIsEnhancingPrompt(false);
        }
    }, [localPrompt, isEnhancingPrompt, onPromptChange, success, toastError]);

    const authenticatedHandleEnhancePrompt = useAuthProtectedCallback(
        handleEnhancePrompt,
        "enhance_prompt"
    );

    // Handle Undo Enhance
    const handleUndoEnhance = useCallback(() => {
        if (originalPrompt !== null) {
            setLocalPrompt(originalPrompt);
            onPromptChange(originalPrompt); // Update global state via debounced prop
            setShowUndoButton(false);
            setOriginalPrompt(null);
            success("Restored original prompt");
        }
    }, [originalPrompt, onPromptChange, success]);

    // Handle Close Tip
    const handleCloseTip = useCallback(() => {
        setShowEnhanceTip(false);
        localStorage.setItem('enhanceTipDismissed', 'true');
    }, []);

    return (
        <div className="rounded-xl bg-card/10 flex-auto flex flex-col mx-2 md:mx-0">
            {/* Header: Title and Buttons */}
            <div className="flex items-center mb-3 px-2">
                <div className="w-3 h-3 bg-green-300 rounded-full animate-pulse mr-2"></div>
                <h3 className="text-base font-medium">Prompt</h3>

                {/* Mobile vs Desktop buttons */}
                {isMobile ? (
                    <div className="ml-auto grid grid-cols-2 gap-2">
                        {/* Mobile Enhance Button */}
                        <button
                            onClick={authenticatedHandleEnhancePrompt}
                            disabled={!localPrompt.trim() || isEnhancingPrompt}
                            className={cn(
                                "text-xs flex items-center justify-center py-1.5 px-2 rounded h-9",
                                !localPrompt.trim() || isEnhancingPrompt
                                    ? "bg-secondary/50 text-muted-foreground cursor-not-allowed"
                                    : "bg-secondary hover:bg-secondary/80 text-foreground/70 hover:text-foreground"
                            )}
                        >
                            {isEnhancingPrompt ? (
                                <div className="mr-1 animate-spin w-3.5 h-3.5 rounded-full border-2 border-t-current border-current/30"></div>
                            ) : (
                                <Wand2 size={14} className="mr-1" />
                            )}
                            <span className="truncate">Enhance</span>
                        </button>

                        {/* Mobile Undo Button */}
                        {showUndoButton ? (
                            <button
                                onClick={handleUndoEnhance}
                                className="text-xs flex items-center justify-center py-1.5 px-2 rounded h-9 bg-secondary hover:bg-secondary/80 text-foreground/70 hover:text-foreground"
                                title="Restore original prompt"
                            >
                                <Undo2 size={14} className="mr-1" />
                                <span className="truncate">Undo</span>
                            </button>
                        ) : (
                            <div className="h-9"></div> // Empty placeholder to maintain grid layout
                        )}
                    </div>
                ) : (
                    <div className="ml-auto flex items-center space-x-2">
                        {/* Desktop Enhance Button and Tip */}
                        <div className="relative">
                            <button
                                onClick={authenticatedHandleEnhancePrompt}
                                disabled={!localPrompt.trim() || isEnhancingPrompt}
                                className={cn(
                                    "text-xs flex items-center px-2 py-1 rounded-md relative",
                                    !localPrompt.trim() || isEnhancingPrompt
                                        ? "bg-card/20 text-muted-foreground cursor-not-allowed"
                                        : "bg-card/40 hover:bg-card/50 transition-colors"
                                )}
                            >
                                {isEnhancingPrompt ? (
                                    <div className="mr-1.5 animate-spin w-3 h-3 rounded-full border-2 border-t-current border-current/30"></div>
                                ) : (
                                    <Wand2 size={12} className="mr-1.5" />
                                )}
                                Enhance
                                {showEnhanceTip && !isMobile && localPrompt.trim() && !isEnhancingPrompt && (
                                    <span className="absolute -top-1 -right-1 w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                                )}
                            </button>

                            {/* Enhance Tip Popover - Only show on desktop */}
                            {showEnhanceTip && !isMobile && (
                                <div className="absolute top-full right-0 mt-1 w-64 p-3 text-sm bg-popover shadow-md rounded-md border border-border/30 z-10">
                                    <div className="flex flex-col gap-2">
                                        <div className="flex items-start gap-2">
                                            <InfoIcon size={14} className="text-green-500 mt-0.5 flex-shrink-0" />
                                            <div>
                                                <p className="font-medium mb-1">Pro Tip:</p>
                                                <p>Using Enhance will significantly improve your video generation quality by refining your prompt with AI.</p>
                                            </div>
                                        </div>
                                        <div className="flex justify-end mt-2">
                                            <Button
                                                size="sm"
                                                variant="outline"
                                                className="text-xs h-7 px-3"
                                                onClick={handleCloseTip}
                                            >
                                                Got it
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>

                        {/* Desktop Undo Button */}
                        {showUndoButton && (
                            <button
                                onClick={handleUndoEnhance}
                                className="text-xs flex items-center px-2 py-1 rounded-md bg-amber-500/10 hover:bg-amber-500/20 text-amber-500 transition-colors"
                                title="Restore original prompt"
                            >
                                <Undo2 size={12} className="mr-1.5" />
                                Undo
                            </button>
                        )}
                    </div>
                )}
            </div>

            {/* Textarea */}
            <textarea
                placeholder="Describe the video you want to create..."
                maxLength={maxLength}
                value={localPrompt}
                onChange={handleLocalPromptChange}
                className="border border-foreground/10 flex-auto w-full h-28 px-3 py-2 rounded-xl bg-card/30 focus:outline-none resize-none"
                disabled={isEnhancingPrompt}
            />

            {/* Error Message */}
            {errorMessage && (
                <div className="mt-4 py-2 px-3 bg-red-500/10 rounded-lg text-sm text-red-400 flex items-center">
                    <AlertCircle size={16} className="mr-2" />
                    {errorMessage}
                </div>
            )}
        </div>
    );
};

export const PromptInput = React.memo(PromptInputComponent);