import { Injectable, Inject } from '@nestjs/common';
import { SupabaseClient } from '@supabase/supabase-js';
import { SUPABASE_CLIENT } from '../providers/supabase.provider';
import { CustomLogger } from './logger.service';
import { DailyGrowthStats, GrowthStatsResponse } from '../controllers/admin-stats.controller';

@Injectable()
export class AdminStatsService {
    constructor(
        @Inject(SUPABASE_CLIENT) private readonly supabase: SupabaseClient,
        private readonly logger: CustomLogger,
    ) {
        this.logger.setContext(AdminStatsService.name);
    }

    /**
     * 获取增长统计数据
     * @param days 统计天数
     * @returns 增长统计数据
     */
    async getGrowthStats(days: number = 7): Promise<GrowthStatsResponse> {
        try {
            // 计算日期范围 - 使用UTC时间避免时区问题
            const now = new Date();
            // 使用UTC方法获取当前UTC日期，避免时区问题
            const endDate = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), 23, 59, 59, 999));
            const startDate = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate() - days + 1, 0, 0, 0, 0));

            // 并行查询所有统计数据以提高性能
            const [
                newUsersData,
                newSubscriptionsData,
                creditsPurchasedData,
                videoTasksSubmittedData,
                imageTasksSubmittedData,
                soundTasksSubmittedData,
                totalRegisteredUsers,
                totalSubscriptions
            ] = await Promise.all([
                this.getNewUsersStats(startDate, endDate),
                this.getNewSubscriptionsStats(startDate, endDate),
                this.getCreditsPurchasedStats(startDate, endDate),
                this.getVideoTasksSubmittedStats(startDate, endDate),
                this.getImageTasksSubmittedStats(startDate, endDate),
                this.getSoundTasksSubmittedStats(startDate, endDate),
                this.getTotalRegisteredUsers(),
                this.getTotalSubscriptions()
            ]);

            // 生成日期序列
            const dateRange = this.generateDateRange(startDate, endDate);

            // 合并统计数据
            const stats: DailyGrowthStats[] = dateRange.map(date => {
                const dateStr = date.toISOString().split('T')[0];
                return {
                    date: dateStr,
                    new_users: newUsersData[dateStr] || 0,
                    new_subscriptions: newSubscriptionsData[dateStr] || 0,
                    credits_purchased: creditsPurchasedData[dateStr] || 0,
                    video_tasks_submitted: videoTasksSubmittedData[dateStr] || 0,
                    image_tasks_submitted: imageTasksSubmittedData[dateStr] || 0,
                    sound_tasks_submitted: soundTasksSubmittedData[dateStr] || 0,
                };
            });

            // 计算汇总数据 - 使用真实的总数而不是时间范围内的增量
            const summary = {
                total_new_users: totalRegisteredUsers, // 真正的总注册用户数
                total_new_subscriptions: totalSubscriptions, // 真正的总订阅数
                total_credits_purchased: 0, // 前端不再显示，设为0
                total_video_tasks_submitted: 0, // 前端不再显示，设为0
                total_image_tasks_submitted: 0, // 前端不再显示，设为0
                total_sound_tasks_submitted: 0, // 前端不再显示，设为0
            };

            return { stats, summary };
        } catch (error) {
            this.logger.error('获取增长统计数据失败', error);
            throw error;
        }
    }

    /**
     * 获取新用户统计
     */
    private async getNewUsersStats(startDate: Date, endDate: Date): Promise<Record<string, number>> {
        // 使用分页查询以避免Supabase的默认限制
        let allData: any[] = [];
        let page = 0;
        const pageSize = 1000;

        while (true) {
            const { data, error } = await this.supabase
                .from('user_profiles')
                .select('created_at')
                .gte('created_at', startDate.toISOString())
                .lte('created_at', endDate.toISOString())
                .range(page * pageSize, (page + 1) * pageSize - 1);

            if (error) {
                this.logger.error('查询新用户统计失败', error);
                throw error;
            }

            if (data.length === 0) {
                break;
            }

            allData = allData.concat(data);

            // 如果返回的数据少于pageSize，说明已经是最后一页
            if (data.length < pageSize) {
                break;
            }

            page++;
        }

        return this.groupByDate(allData, 'created_at');
    }

    /**
     * 获取新订阅统计
     * 使用user_memberships表统计真正的新订阅，避免将续费计入新订阅
     */
    private async getNewSubscriptionsStats(startDate: Date, endDate: Date): Promise<Record<string, number>> {
        // 使用分页查询以避免Supabase的默认限制
        let allData: any[] = [];
        let page = 0;
        const pageSize = 1000;

        while (true) {
            const { data, error } = await this.supabase
                .from('user_memberships')
                .select('created_at')
                .gte('created_at', startDate.toISOString())
                .lte('created_at', endDate.toISOString())
                .range(page * pageSize, (page + 1) * pageSize - 1);

            if (error) {
                this.logger.error('查询新订阅统计失败', error);
                throw error;
            }

            if (data.length === 0) {
                break;
            }

            allData = allData.concat(data);

            if (data.length < pageSize) {
                break;
            }

            page++;
        }

        return this.groupByDate(allData, 'created_at');
    }

    /**
     * 获取积分购买统计
     */
    private async getCreditsPurchasedStats(startDate: Date, endDate: Date): Promise<Record<string, number>> {
        // 使用分页查询以避免Supabase的默认限制
        let allData: any[] = [];
        let page = 0;
        const pageSize = 1000;

        while (true) {
            const { data, error } = await this.supabase
                .from('credit_transactions')
                .select('created_at, amount')
                .eq('type', 'direct_purchase')
                .eq('status', 'completed')
                .gte('created_at', startDate.toISOString())
                .lte('created_at', endDate.toISOString())
                .range(page * pageSize, (page + 1) * pageSize - 1);

            if (error) {
                this.logger.error('查询积分购买统计失败', error);
                throw error;
            }

            if (data.length === 0) {
                break;
            }

            allData = allData.concat(data);

            if (data.length < pageSize) {
                break;
            }

            page++;
        }

        return this.groupByDateWithSum(allData, 'created_at', 'amount');
    }

    /**
     * 获取视频任务提交统计
     */
    private async getVideoTasksSubmittedStats(startDate: Date, endDate: Date): Promise<Record<string, number>> {
        // 使用分页查询以避免Supabase的默认限制
        let allData: any[] = [];
        let page = 0;
        const pageSize = 1000;

        while (true) {
            const { data, error } = await this.supabase
                .from('video_gen_tasks')
                .select('created_at')
                .gte('created_at', startDate.toISOString())
                .lte('created_at', endDate.toISOString())
                .range(page * pageSize, (page + 1) * pageSize - 1);

            if (error) {
                this.logger.error('查询视频任务统计失败', error);
                throw error;
            }

            if (data.length === 0) {
                break;
            }

            allData = allData.concat(data);

            if (data.length < pageSize) {
                break;
            }

            page++;
        }

        return this.groupByDate(allData, 'created_at');
    }

    /**
     * 获取图片任务提交统计
     */
    private async getImageTasksSubmittedStats(startDate: Date, endDate: Date): Promise<Record<string, number>> {
        // 使用分页查询以避免Supabase的默认限制
        let allData: any[] = [];
        let page = 0;
        const pageSize = 1000;

        while (true) {
            const { data, error } = await this.supabase
                .from('pic_gen_tasks')
                .select('created_at')
                .gte('created_at', startDate.toISOString())
                .lte('created_at', endDate.toISOString())
                .gte('priority', 0)
                .range(page * pageSize, (page + 1) * pageSize - 1);

            if (error) {
                this.logger.error('查询图片任务统计失败', error);
                throw error;
            }

            if (data.length === 0) {
                break;
            }

            allData = allData.concat(data);

            if (data.length < pageSize) {
                break;
            }

            page++;
        }

        return this.groupByDate(allData, 'created_at');
    }

    /**
     * 获取声音任务提交统计
     */
    private async getSoundTasksSubmittedStats(startDate: Date, endDate: Date): Promise<Record<string, number>> {
        // 使用分页查询以避免Supabase的默认限制
        let allData: any[] = [];
        let page = 0;
        const pageSize = 1000;

        while (true) {
            const { data, error } = await this.supabase
                .from('sound_gen_tasks')
                .select('created_at')
                .gte('created_at', startDate.toISOString())
                .lte('created_at', endDate.toISOString())
                .range(page * pageSize, (page + 1) * pageSize - 1);

            if (error) {
                this.logger.error('查询声音任务统计失败', error);
                throw error;
            }

            if (data.length === 0) {
                break;
            }

            allData = allData.concat(data);

            if (data.length < pageSize) {
                break;
            }

            page++;
        }

        return this.groupByDate(allData, 'created_at');
    }

    /**
     * 按日期分组数据
     */
    private groupByDate(data: any[], dateField: string): Record<string, number> {
        const result: Record<string, number> = {};

        data.forEach(item => {
            if (item[dateField]) {
                const date = new Date(item[dateField]).toISOString().split('T')[0];
                result[date] = (result[date] || 0) + 1;
            }
        });

        return result;
    }

    /**
     * 按日期分组并求和数据
     */
    private groupByDateWithSum(data: any[], dateField: string, sumField: string): Record<string, number> {
        const result: Record<string, number> = {};

        data.forEach(item => {
            if (item[dateField]) {
                const date = new Date(item[dateField]).toISOString().split('T')[0];
                result[date] = (result[date] || 0) + (item[sumField] || 0);
            }
        });

        return result;
    }

    /**
     * 生成日期范围
     */
    private generateDateRange(startDate: Date, endDate: Date): Date[] {
        const dates: Date[] = [];
        const currentDate = new Date(startDate);

        // 确保使用UTC时间进行日期比较
        const endDateOnly = new Date(Date.UTC(endDate.getUTCFullYear(), endDate.getUTCMonth(), endDate.getUTCDate()));

        while (currentDate <= endDateOnly) {
            dates.push(new Date(currentDate));
            currentDate.setUTCDate(currentDate.getUTCDate() + 1);
        }

        return dates;
    }

    /**
     * 获取总注册用户数
     */
    private async getTotalRegisteredUsers(): Promise<number> {
        const { count, error } = await this.supabase
            .from('user_profiles')
            .select('*', { count: 'exact', head: true });

        if (error) {
            this.logger.error('查询总用户数失败', error);
            throw error;
        }

        return count || 0;
    }

    /**
     * 获取总订阅数
     * 使用user_memberships表统计总订阅数，避免将续费计入总数
     */
    private async getTotalSubscriptions(): Promise<number> {
        const { count, error } = await this.supabase
            .from('user_memberships')
            .select('*', { count: 'exact', head: true });

        if (error) {
            this.logger.error('查询总订阅数失败', error);
            throw error;
        }

        return count || 0;
    }


} 