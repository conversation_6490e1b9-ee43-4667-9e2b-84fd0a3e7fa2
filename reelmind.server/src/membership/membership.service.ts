import {
    Injectable,
    Inject,
    BadRequestException,
    NotFoundException,
    forwardRef,
} from '@nestjs/common';
import { SupabaseClient } from '@supabase/supabase-js';
import { SUPABASE_CLIENT } from '../common/providers/supabase.provider';
import { CustomLogger } from '../common/services/logger.service';
import {
    MembershipResponseDto,
    MembershipSubscriptionDto,
    CancelMembershipDto,
} from './dto/membership.dto';
import { MembershipInterestService } from './membership-interest.service';
import { MembershipPlanService } from './membership-plan.service';
import { MembershipLevel, MembershipStatus } from './constant';
import { CreditsService } from '../credits/credits.service';

/**
 * Customer information for subscription updates
 */
interface CustomerInfo {
    customerId?: string;
    cancelAtPeriodEnd?: boolean;
    isRenewal?: boolean;
    invoiceId?: string;
}

/**
 * Subscription update data structure
 */
interface SubscriptionUpdateData {
    updated_at: string;
    current_period_end?: string;
    expires_at?: string;
    cancel_at_period_end?: boolean;
    is_active?: boolean;
    status?: MembershipStatus;
}

// 会员优先级配置
export const DEFAULT_MEMBERSHIP_PRIORITIES = {
    [MembershipLevel.FREE]: 0,
    [MembershipLevel.PRO]: 20,
    [MembershipLevel.MAX]: 30,
    [MembershipLevel.UNLIMITED]: 50,
};

@Injectable()
export class MembershipService {
    constructor(
        @Inject(SUPABASE_CLIENT) private readonly supabase: SupabaseClient,
        private readonly logger: CustomLogger,
        private readonly interestService: MembershipInterestService,
        private readonly planService: MembershipPlanService,
        @Inject(forwardRef(() => CreditsService))
        private readonly creditsService: CreditsService,
    ) {
        this.logger.setContext(MembershipService.name);
    }

    /**
     * 获取用户会员信息
     * @param userId 用户ID
     * @returns 会员信息
     */
    async getUserMembership(
        userId: string,
    ): Promise<MembershipResponseDto | null> {
        try {
            // 一次性查询用户会员信息和计划信息，避免串行查询
            const { data, error } = await this.supabase
                .from('user_memberships')
                .select(`
                    *,
                    membership_plans (
                        plan_name,
                        level
                    )
                `)
                .eq('user_id', userId)
                .eq('is_active', true)
                .order('created_at', { ascending: false })
                .limit(1)
                .single();

            if (error || !data) {
                return null;
            }

            // 检查会员是否已过期
            const now = new Date();
            const expiresAt = new Date(data.expires_at);

            // 如果已过期，将is_active设置为false
            if (expiresAt < now && data.is_active) {
                this.logger.log(`用户会员已过期: ${userId}, 过期时间: ${expiresAt.toISOString()}`);

                // 更新数据库中的会员状态
                const { error: updateError } = await this.supabase
                    .from('user_memberships')
                    .update({
                        is_active: false,
                        updated_at: now.toISOString(),
                    })
                    .eq('id', data.id);

                if (updateError) {
                    this.logger.error(`更新过期会员状态失败: ${data.id}`, updateError);
                } else {
                    // 更新本地数据
                    data.is_active = false;
                }
            }

            // 从关联查询中获取计划名称
            const planName = data.membership_plans?.plan_name || 'Free';

            // 计算剩余天数
            const periodEndDate = new Date(
                data.current_period_end || data.expires_at,
            );
            const daysLeft = Math.ceil(
                (periodEndDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24),
            );

            return {
                ...data,
                plan_name: planName,
                level_name: MembershipLevel[data.level],
                days_left: daysLeft > 0 ? daysLeft : 0,
            };
        } catch (error) {
            this.logger.error(`获取用户会员信息失败: ${userId}`, error);
            return null;
        }
    }

    /**
     * 根据ID获取会员计划
     * @param planId 计划ID
     * @returns 会员计划
     */
    async getMembershipPlanById(planId: string) {
        return this.planService.getMembershipPlanById(planId);
    }

    /**
     * 开通会员订阅
     * @param userId 用户ID
     * @param createMembershipDto 创建会员订阅请求
     * @returns 创建的会员信息
     */
    async activateMembershipSubscription(
        userId: string,
        createMembershipDto: MembershipSubscriptionDto,
    ): Promise<boolean> {
        try {
            const {
                subscription_id,
                plan_id,
                billing_cycle,
                current_period_end,
                cancel_at_period_end,
                paymentId,
            } = createMembershipDto;

            // 检查计划是否存在
            const plan = await this.getMembershipPlanById(plan_id);

            if (!plan) {
                throw new NotFoundException(`会员计划不存在: ${plan_id}`);
            };

            // 创建用户会员记录
            const { error } = await this.supabase
                .from('user_memberships')
                .insert({
                    user_id: userId,
                    plan_id,
                    is_active: true,
                    subscription_id,
                    billing_cycle,
                    expires_at: current_period_end,
                    cancel_at_period_end: cancel_at_period_end || false,
                })
                .select('*')
                .single();

            if (error) {
                this.logger.error(`创建会员订阅失败: ${error.message}`, error);
                throw new BadRequestException(`创建会员订阅失败: ${error.message}`);
            }

            // 发放初始会员积分 - 传递已查询的plan对象，避免重复查询
            try {
                const {
                    success,
                    transaction,
                    message,
                    amount
                } = await this.creditsService.grantMembershipInitialCredits(userId, plan_id, paymentId, plan);
                if (!success) {
                    this.logger.error(`发放会员首次开通积分失败: ${transaction}`);
                    throw new BadRequestException(message);
                }
                this.logger.log(`已为用户 ${userId} 发放 ${amount} 会员首次开通积分`);
            } catch (creditError) {
                // 记录积分发放失败，但不影响会员订阅流程
                this.logger.error(`会员订阅成功但初始积分发放失败: ${creditError.message}`, creditError);
            }

            return true;
        } catch (error) {
            this.logger.error(`开通会员订阅失败: ${error.message}`, error);
            throw error;
        }
    }

    /**
     * 取消会员订阅
     * @param userId 用户ID
     * @param cancelDto 取消会员订阅请求
     * @returns 是否成功
     */
    async cancelMembershipSubscription(
        userId: string,
        cancelDto: CancelMembershipDto,
    ): Promise<boolean> {
        try {
            const { subscription_id } = cancelDto;

            // 获取用户会员信息
            const { data, error } = await this.supabase
                .from('user_memberships')
                .select('*')
                .eq('user_id', userId)
                .eq('subscription_id', subscription_id)
                .eq('is_active', true)
                .single();

            if (error || !data) {
                this.logger.error(
                    `取消会员订阅失败，未找到对应订阅: ${userId}, ${subscription_id}`,
                );
                throw new BadRequestException('未找到有效的会员订阅');
            }

            // 更新会员记录为非活跃
            const { error: updateError } = await this.supabase
                .from('user_memberships')
                .update({
                    is_active: false,
                    subscription_status: 'canceled',
                    updated_at: new Date().toISOString(),
                })
                .eq('id', data.id);

            if (updateError) {
                this.logger.error(`更新会员状态失败: ${data.id}`, updateError);
                return false;
            }

            return true;
        } catch (error) {
            this.logger.error(`取消会员订阅失败: ${userId}`, error);
            if (error instanceof BadRequestException) {
                throw error;
            }
            return false;
        }
    }



    /**
     * Handle subscription updates (renewals, status changes, etc.)
     * @param subscriptionId Subscription ID
     * @param status Subscription status
     * @param currentPeriodEnd Current period end time
     * @param customerInfo Customer information
     * @returns Whether the update was successful
     */
    async handleSubscriptionUpdate(
        subscriptionId: string,
        status: string,
        currentPeriodEnd: Date,
        customerInfo?: CustomerInfo,
    ): Promise<boolean> {
        try {
            this.logger.log(`处理订阅更新: ${subscriptionId}, 状态: ${status}`);

            // Fetch current subscription
            const subscription = await this.fetchSubscription(subscriptionId);
            if (!subscription) {
                return false;
            }

            // Get plan details
            const plan = await this.getMembershipPlanById(subscription.plan_id);
            const isRenewal = this.determineIfRenewal(subscription, currentPeriodEnd, customerInfo);

            // Build update data
            const updateData = this.buildUpdateData(currentPeriodEnd, customerInfo, status, subscription);

            // Update subscription record
            await this.updateSubscriptionRecord(subscription.id, updateData);

            // Handle renewal credits if applicable
            if (isRenewal && status === 'active' && plan) {
                await this.handleRenewalCredits(subscription.user_id, subscription.plan_id, plan, customerInfo);
            }

            this.logger.log(`订阅更新完成: ${subscriptionId}`);
            return true;
        } catch (error) {
            this.logger.error(`处理订阅更新失败: ${error.message}`, error);
            throw error;
        }
    }

    /**
     * Fetch subscription by ID
     */
    private async fetchSubscription(subscriptionId: string) {
        const { data: subscriptions, error: fetchError } = await this.supabase
            .from('user_memberships')
            .select('*, user:user_id(*)')
            .eq('subscription_id', subscriptionId)
            .order('created_at', { ascending: false })
            .limit(1);

        if (fetchError) {
            this.logger.error(`获取订阅信息失败: ${fetchError.message}`, fetchError);
            throw new BadRequestException(`获取订阅信息失败: ${fetchError.message}`);
        }

        if (!subscriptions || subscriptions.length === 0) {
            this.logger.error(`未找到订阅: ${subscriptionId}`);
            return null;
        }

        return subscriptions[0];
    }

    /**
     * Determine if this is a renewal
     */
    private determineIfRenewal(subscription: any, currentPeriodEnd: Date, customerInfo?: CustomerInfo): boolean {
        const isNewPeriod = new Date(subscription.current_period_end) < currentPeriodEnd;
        const isRenewal = customerInfo?.isRenewal || isNewPeriod;

        if (isRenewal) {
            this.logger.log(`检测到续费: 用户=${subscription.user_id}, 订阅=${subscription.subscription_id}, 发票=${customerInfo?.invoiceId || '未知'}`);
        }

        return isRenewal;
    }

    /**
     * Build subscription update data
     */
    private buildUpdateData(
        currentPeriodEnd: Date,
        customerInfo: CustomerInfo | undefined,
        status: string,
        subscription: any
    ): SubscriptionUpdateData {
        const updateData: SubscriptionUpdateData = {
            updated_at: new Date().toISOString(),
        };

        // Update period end if provided
        if (currentPeriodEnd) {
            updateData.current_period_end = currentPeriodEnd.toISOString();
            updateData.expires_at = currentPeriodEnd.toISOString();
        }

        // Update cancellation status
        if (customerInfo?.cancelAtPeriodEnd !== undefined) {
            updateData.cancel_at_period_end = customerInfo.cancelAtPeriodEnd;
        }

        // Update subscription status
        if (status) {
            const statusMapping = this.mapSubscriptionStatus(status, subscription);
            Object.assign(updateData, statusMapping);
        }

        return updateData;
    }

    /**
     * Map external subscription status to internal status
     */
    private mapSubscriptionStatus(status: string, subscription: any): Partial<SubscriptionUpdateData> {
        const statusMap: Record<string, Partial<SubscriptionUpdateData>> = {
            'active': {
                is_active: true,
                status: MembershipStatus.ACTIVE
            },
            'canceled': {
                status: MembershipStatus.CANCELED,
                is_active: new Date() < new Date(subscription.current_period_end)
            },
            'past_due': {
                status: MembershipStatus.GRACE_PERIOD
                // Keep active during grace period
            },
            'trialing': {
                status: MembershipStatus.TRIAL,
                is_active: true
            }
        };

        // Handle multiple statuses for pending
        if (['incomplete', 'incomplete_expired', 'unpaid'].includes(status)) {
            return {
                status: MembershipStatus.PENDING,
                is_active: false
            };
        }

        const mappedStatus = statusMap[status];
        if (!mappedStatus) {
            this.logger.warn(`收到未知的订阅状态: ${status}, 用户: ${subscription.user_id}, 订阅: ${subscription.subscription_id}`);
            return {};
        }

        return mappedStatus;
    }

    /**
     * Update subscription record in database
     */
    private async updateSubscriptionRecord(subscriptionId: string, updateData: SubscriptionUpdateData): Promise<void> {
        const { error: updateError } = await this.supabase
            .from('user_memberships')
            .update(updateData)
            .eq('id', subscriptionId);

        if (updateError) {
            this.logger.error(`更新订阅记录失败: ${updateError.message}`, updateError);
            throw new BadRequestException(`更新订阅记录失败: ${updateError.message}`);
        }
    }

    /**
     * Handle renewal credits for active subscriptions
     */
    private async handleRenewalCredits(
        userId: string,
        planId: string,
        plan: any,
        customerInfo?: CustomerInfo
    ): Promise<void> {
        try {
            const membershipType = plan.plan_name || '未知';

            // Only grant credits for non-free memberships
            if (this.isFreeplan(membershipType)) {
                this.logger.log(`免费会员，不发放积分: 用户=${userId}, 类型=${membershipType}`);
                return;
            }

            // Prepare credit parameters
            const creditParams = this.buildCreditParams(planId, plan, customerInfo, membershipType);

            // Grant monthly credits
            const creditResult = await this.creditsService.grantMembershipMonthlyCredits(userId, planId, creditParams);

            this.logCreditResult(userId, membershipType, creditResult);
        } catch (creditError) {
            // Log credit failure but don't affect subscription update flow
            this.logger.error(`订阅更新成功但月度积分刷新失败: ${creditError.message}`, creditError);
        }
    }

    /**
     * Check if plan is free
     */
    private isFreeplan(membershipType: string): boolean {
        const lowerType = membershipType.toLowerCase();
        return lowerType === 'free' || lowerType.includes('免费');
    }

    /**
     * Build credit parameters for renewal
     */
    private buildCreditParams(planId: string, plan: any, customerInfo?: CustomerInfo, membershipType?: string): any {
        const creditParams: any = {
            planId,
            plan,
            isRenewal: true
        };

        if (customerInfo?.invoiceId) {
            creditParams.invoiceId = customerInfo.invoiceId;
            creditParams.description = `${membershipType}会员续费赠送积分 (发票: ${customerInfo.invoiceId})`;
        }

        return creditParams;
    }

    /**
     * Log credit operation result
     */
    private logCreditResult(userId: string, membershipType: string, creditResult: any): void {
        if (creditResult?.success) {
            this.logger.log(`会员续费积分发放成功: 用户=${userId}, 类型=${membershipType}, 新余额=${creditResult.newBalance}, 旧余额=${creditResult.previousBalance}, 交易=${creditResult.transaction}`);
        } else if (creditResult) {
            this.logger.warn(`会员续费积分发放失败: 用户=${userId}, 原因=${creditResult.message}`);
        }
    }



    /**
     * 获取用户会员优先级
     * @param userId 用户ID
     * @returns 会员优先级
     */
    async getUserMembershipPriority(userId: string): Promise<number> {
        // 获取用户会员信息
        const membership = await this.getUserMembership(userId);

        // 如果用户没有会员或会员未激活，返回默认优先级0
        if (!membership || !membership.is_active || !membership.plan_id) {
            return 0;
        }

        // 从会员权益表中获取视频生成优先级
        const priority = await this.interestService.getVideoGenPriority(membership.plan_id);

        // 如果权益表中没有设置优先级，则使用默认配置
        if (priority === undefined || priority === null) {
            return DEFAULT_MEMBERSHIP_PRIORITIES[membership.level] || 0;
        }

        return priority;
    }

    /**
     * 检查用户是否有特定的会员权益
     * @param userId 用户ID
     * @param interestKey 权益键名
     * @returns 权益值，如果没有返回null
     */
    async checkUserMembershipInterest(
        userId: string,
        interestKey: string,
    ): Promise<any> {
        const membership = await this.getUserMembership(userId);
        if (!membership || !membership.is_active || !membership.plan_id) {
            return null;
        }

        return this.interestService.getMembershipInterestValue(
            membership.plan_id,
            interestKey,
        );
    }

    /**
     * 获取所有用户订阅信息（管理员功能）
     * @param page 页码
     * @param limit 每页数量
     * @param search 搜索关键词
     * @returns 用户订阅列表和总数
     */
    async getAllUserSubscriptions(
        page: number = 1,
        limit: number = 20,
        search?: string
    ): Promise<{
        data: any[];
        total: number;
        page: number;
        limit: number;
        totalPages: number;
    }> {
        try {
            const offset = (page - 1) * limit;

            // 构建查询
            let query = this.supabase
                .from('user_memberships')
                .select(`
                    id,
                    user_id,
                    is_active,
                    created_at,
                    updated_at,
                    expires_at,
                    subscription_id,
                    cancel_at_period_end,
                    plan_id,
                    user_profiles!inner (
                        nickname,
                        user_id
                    ),
                    membership_plans (
                        plan_name,
                        level
                    )
                `)
                .order('created_at', { ascending: false });

            // 如果有搜索条件，添加搜索过滤
            if (search && search.trim()) {
                // 这里我们需要通过用户昵称或邮箱搜索
                // 由于Supabase的限制，我们先获取匹配的用户ID
                const { data: matchedProfiles } = await this.supabase
                    .from('user_profiles')
                    .select('user_id')
                    .or(`nickname.ilike.%${search}%`);

                if (matchedProfiles && matchedProfiles.length > 0) {
                    const userIds = matchedProfiles.map((profile: any) => profile.user_id);
                    query = query.in('user_id', userIds);
                } else {
                    // 如果没有匹配的用户，返回空结果
                    return {
                        data: [],
                        total: 0,
                        page,
                        limit,
                        totalPages: 0
                    };
                }
            }

            // 获取总数
            const countQuery = this.supabase
                .from('user_memberships')
                .select('*', { count: 'exact', head: true });

            // 应用相同的搜索过滤
            if (search && search.trim()) {
                const { data: matchedProfiles } = await this.supabase
                    .from('user_profiles')
                    .select('user_id')
                    .or(`nickname.ilike.%${search}%`);

                if (matchedProfiles && matchedProfiles.length > 0) {
                    const userIds = matchedProfiles.map((profile: any) => profile.user_id);
                    countQuery.in('user_id', userIds);
                }
            }

            const { count: total } = await countQuery;

            // 获取分页数据
            const { data: subscriptions, error } = await query
                .range(offset, offset + limit - 1);

            if (error) {
                this.logger.error('获取用户订阅列表失败', error);
                throw new BadRequestException('获取用户订阅列表失败');
            }

            // 获取用户邮箱信息
            const userIds = subscriptions?.map((sub: any) => sub.user_id) || [];
            const userEmails: { [key: string]: string } = {};

            if (userIds.length > 0) {
                // 通过Supabase auth API获取用户邮箱
                const { data: authUsers, error: authError } = await this.supabase.auth.admin.listUsers();

                if (!authError && authUsers?.users) {
                    authUsers.users.forEach((user: any) => {
                        if (userIds.includes(user.id)) {
                            userEmails[user.id] = user.email || '';
                        }
                    });
                }
            }

            // 格式化数据
            const formattedData = subscriptions?.map((subscription: any) => ({
                id: subscription.id,
                user_id: subscription.user_id,
                nickname: subscription.user_profiles?.nickname || '未设置',
                email: userEmails[subscription.user_id] || '未知',
                plan_name: subscription.membership_plans?.plan_name || '未知计划',
                billing_cycle: subscription.subscription_id ? '订阅制' : '一次性',
                is_active: subscription.is_active,
                created_at: subscription.created_at,
                expires_at: subscription.expires_at,
                subscription_id: subscription.subscription_id,
                cancel_at_period_end: subscription.cancel_at_period_end
            })) || [];

            const totalPages = Math.ceil((total || 0) / limit);

            return {
                data: formattedData,
                total: total || 0,
                page,
                limit,
                totalPages
            };
        } catch (error) {
            this.logger.error('获取用户订阅列表异常', error);
            throw new BadRequestException('获取用户订阅列表失败');
        }
    }
}
