"use client"

import React from 'react';
import { Sparkles, Gift } from "lucide-react";
import { cn } from "@/lib/utils";
import { useControlPanel } from './ControlPanelContext';
import { useQuery } from "@tanstack/react-query";
import { creditsApi } from "@/lib/api/credits";
import useAuthStore from "@/store/useAuthStore";
import { useRouter } from 'next/navigation';
import { GiftIcon } from "@/components/credits/gift-icon";
import useCreditsStore from "@/store/useCreditsStore";
import { membershipApi } from "@/lib/api/membership";

export const GenerateButton = () => {
  const router = useRouter();
  const { isAuthenticated } = useAuthStore();
  const {
    canClaimBonus,
    isClaimingBonus,
    checkClaimStatus,
    hasCheckedClaimStatus
  } = useCreditsStore();

  const {
    isGenerating,
    isLoadingModels,
    isLoadingPresignedUrl,
    isLoadingTaskPrice,
    selectedModelId,
    modelType,
    globalPrompt,
    images,
    videoCost,
    handleGenerateVideo,
    isStartToEndModel,
    startImages,
    endImages,
    isStartImageUploading,
    isEndImageUploading
  } = useControlPanel();

  // 使用 react-query 获取积分余额（与UserNav保持一致）
  const { data: currentBalance, isLoading: isLoadingCredits } = useQuery({
    queryKey: ["credits", "balance"],
    queryFn: async () => {
      const data = await creditsApi.getUserBalance();
      return data.data.balance;
    },
    enabled: !!isAuthenticated,
  });

  // 使用 react-query 获取用户会员信息
  const { data: userMembership } = useQuery({
    queryKey: ["membership", "user"],
    queryFn: async () => {
      const data = await membershipApi.getUserMembership();
      return data;
    },
    enabled: !!isAuthenticated,
  });

  // 检查是否为UNLIMITED会员
  const isUnlimitedMember = userMembership?.plan_name === 'UNLIMITED' && userMembership?.is_active;

  // 检查新用户积分领取状态
  React.useEffect(() => {
    if (isAuthenticated && !hasCheckedClaimStatus && !isClaimingBonus) {
      console.log('[GenerateButton] Checking claim status');
      checkClaimStatus();
    }
  }, [isAuthenticated, hasCheckedClaimStatus, isClaimingBonus, checkClaimStatus]);

  // Check if user has insufficient credits (UNLIMITED members never have insufficient credits)
  const hasInsufficientCredits = !isUnlimitedMember && currentBalance !== undefined && videoCost !== undefined && currentBalance < videoCost;

  // 判断是否应该显示领取按钮：用户积分为0且有领取资格，但UNLIMITED会员不需要
  const shouldShowClaimButton = !isUnlimitedMember && currentBalance === 0 && canClaimBonus;

  // Determine if the button should be disabled - 仅在不是积分不足的情况下应用其他禁用条件
  const isDisabled = hasInsufficientCredits
    ? false // 余额不足时按钮始终可点击
    : (
      isGenerating ||
      !selectedModelId ||
      isLoadingModels ||
      isLoadingPresignedUrl ||
      (isStartToEndModel && (
        startImages.length === 0 ||
        endImages.length === 0 ||
        isStartImageUploading ||
        isEndImageUploading ||
        startImages[0]?.startsWith('data:') ||
        endImages[0]?.startsWith('data:')
      )) ||
      (!isStartToEndModel && ((!modelType || modelType === 'text-to-video') && !globalPrompt.trim())) ||
      (!isStartToEndModel && modelType === 'image-to-video' && images.length === 0) ||
      (!isStartToEndModel && modelType === 'video-to-video' && images.length === 0)
    );

  // Determine the button's visual style
  const buttonStyle = cn(
    "w-full py-3 rounded-xl font-medium flex items-center justify-center transition-all relative z-10 overflow-hidden cursor-pointer",
    // 当余额不足时，按钮始终有活跃的样式
    hasInsufficientCredits
      ? "bg-gradient-to-r from-amber-500 to-orange-600 hover:brightness-110 text-white"
      : isUnlimitedMember
        ? "bg-gradient-to-r from-amber-400 via-purple-500 to-pink-500 hover:brightness-110 text-white shadow-lg shadow-purple-500/25 relative before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] hover:before:translate-x-[100%] before:transition-transform before:duration-1000"
        : isGenerating || isLoadingModels || isLoadingPresignedUrl || isStartImageUploading || isEndImageUploading
          ? "bg-gray-500/50 cursor-wait"
          : !selectedModelId
            ? "bg-gray-500/30 cursor-not-allowed"
            : (isStartToEndModel && (
              startImages.length === 0 ||
              endImages.length === 0 ||
              startImages[0]?.startsWith('data:') ||
              endImages[0]?.startsWith('data:')
            )) ||
              (!isStartToEndModel && ((!modelType || modelType === 'text-to-video') && !globalPrompt.trim())) ||
              (!isStartToEndModel && modelType === 'image-to-video' && images.length === 0) ||
              (!isStartToEndModel && modelType === 'video-to-video' && images.length === 0)
              ? "bg-gray-500/30 cursor-not-allowed"
              : "text-white animate-gradient-bg bg-gradient-to-r from-red-600 via-orange-500 via-blue-500 to-pink-600"
  );

  const handleButtonClick = () => {
    if (hasInsufficientCredits) {
      // 直接跳转到会员页面，不显示弹框
      router.push('/membership');
    } else {
      handleGenerateVideo();
    }
  };

  return (
    <div className="p-2.5">
      <div className="mb-2 text-sm flex justify-between">
        {/* 成本信息 - 始终显示以保持布局稳定 */}
        <div>
          <span className="text-muted-foreground">Cost:</span>
          {isLoadingTaskPrice ? (
            <span className="ml-2 text-muted-foreground">
              <div className="inline-block w-3 h-3 border border-current border-t-transparent rounded-full animate-spin mr-1"></div>
              Loading...
            </span>
          ) : isUnlimitedMember ? (
            <span className="ml-2 font-bold text-transparent bg-gradient-to-r from-amber-400 via-purple-500 to-pink-500 bg-clip-text">
              Free (Unlimited) ✨
            </span>
          ) : videoCost !== undefined ? (
            <span className="font-bold ml-2">{videoCost} Credits</span>
          ) : (
            <span className="ml-2 text-muted-foreground">--</span>
          )}
        </div>

        {/* 只有在有余额数据时才显示，UNLIMITED会员显示无限符号 */}
        {!isLoadingCredits && (isUnlimitedMember || currentBalance !== undefined) && (
          <div>
            <span className="text-muted-foreground">Balance:</span>
            <span className={`font-bold ml-2 ${hasInsufficientCredits ? 'text-red-500' : ''} ${isUnlimitedMember ? 'text-transparent bg-gradient-to-r from-amber-400 via-purple-500 to-pink-500 bg-clip-text text-xl unlimited-infinity' : ''}`}>
              {isUnlimitedMember ? '∞' : `${currentBalance} Credits`}
            </span>
          </div>
        )}
      </div>

      {/* 隐藏的GiftIcon组件用于触发模态框 */}
      <div className="hidden">
        <GiftIcon variant="button" size="sm" />
      </div>

      {/* 根据条件显示不同的按钮 */}
      {shouldShowClaimButton ? (
        // 显示领取积分按钮 - 使用与Generate按钮相同的样式
        <button
          onClick={() => {
            // 触发礼品图标的点击事件
            const giftButton = document.querySelector('[data-gift-button]') as HTMLButtonElement;
            if (giftButton) {
              giftButton.click();
            }
          }}
          disabled={isClaimingBonus}
          className={cn(
            "w-full py-3 rounded-xl font-medium flex items-center justify-center transition-all relative z-10",
            isClaimingBonus
              ? "bg-gray-500/50 cursor-wait"
              : "bg-gradient-to-r from-amber-500 to-orange-600 hover:brightness-110 text-white"
          )}
        >
          {isClaimingBonus ? (
            <>
              <div className="mr-2 animate-spin w-4 h-4 rounded-full border-2 border-t-white border-white/30"></div>
              Getting credits...
            </>
          ) : (
            <>
              <Gift className="mr-2 h-4 w-4" />
              Get 88 Credits
            </>
          )}
        </button>
      ) : (
        <>
          {/* 只有在不显示领取按钮时才显示礼品图标 */}
          {canClaimBonus && (
            <div className="mb-2">
              <GiftIcon variant="button" size="sm" />
            </div>
          )}

          <button
            onClick={handleButtonClick}
            disabled={isDisabled}
            className={buttonStyle}
          >
            {isGenerating ? (
              <>
                <div className="mr-2 animate-spin w-4 h-4 rounded-full border-2 border-t-white border-white/30"></div>
                Submitting...
              </>
            ) : isLoadingModels ? (
              <>
                <div className="mr-2 animate-spin w-4 h-4 rounded-full border-2 border-t-white border-white/30"></div>
                Loading Models...
              </>
            ) : isLoadingPresignedUrl ? (
              <>
                <div className="mr-2 animate-spin w-4 h-4 rounded-full border-2 border-t-white border-white/30"></div>
                Uploading Asset...
              </>
            ) : isStartImageUploading || isEndImageUploading ? (
              <>
                <div className="mr-2 animate-spin w-4 h-4 rounded-full border-2 border-t-white border-white/30"></div>
                Uploading Images...
              </>
            ) : isClaimingBonus ? (
              <>
                <div className="mr-2 animate-spin w-4 h-4 rounded-full border-2 border-t-white border-white/30"></div>
                Claiming Credits...
              </>
            ) : hasInsufficientCredits ? (
              <>
                <Sparkles className="mr-2 h-4 w-4" />
                Get More Credits
              </>
            ) : (
              <>
                <Sparkles className="mr-2 h-4 w-4" />
                {isUnlimitedMember ? (
                  <span className="flex items-center gap-1">
                    Generate Video
                    <span className="text-amber-200">✨</span>
                    <span className="text-purple-200">⚡</span>
                  </span>
                ) : (
                  'Generate Video'
                )}
              </>
            )}
          </button>
        </>
      )}
    </div>
  );
};
