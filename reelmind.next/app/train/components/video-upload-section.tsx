"use client"

import { use<PERSON><PERSON>back, useEffect, useState } from "react"
import { useDropzone } from "react-dropzone"
import { FileVideo, Loader2 } from "lucide-react"
import { useToast } from "@/hooks/use-sonner-toast"
import { MAX_SIZE_BYTES } from "../types"
import { formatBytes } from "@/lib/utils"
import { VideoCard } from "./video-card"
import { AddVideoCard } from "./add-video-card"
import { useTrainStore } from "@/store/useTrainStore"
import useAuthStore from "@/store/useAuthStore"
import { useTheme } from "next-themes"
import { cn } from "@/lib/utils"

// 声明全局Window接口的扩展
declare global {
  interface Window {
    uploadTrainingVideos?: () => Promise<boolean>;
  }
}

export function VideoUploadSection() {
  const toast = useToast()
  const { theme } = useTheme()
  const isDarkTheme = theme === 'dark'
  const { videos, totalSize, addVideos, updateTotalSize, updateVideoUrl } = useTrainStore()
  const { session } = useAuthStore()
  const [isUploading, setIsUploading] = useState(false)
  const [preparingVideos, setPreparingVideos] = useState<Set<string>>(new Set())

  // 获取视频预签名URL并暂存
  const getPresignedUrl = async (file: File) => {
    try {
      // 获取预签名URL
      const response = await fetch("/api/train/upload", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          filename: file.name,
          contentType: file.type,
          size: file.size,
        }),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || "Failed to get presigned URL")
      }

      const data = await response.json()
      // 重要：确保返回的URL是publicUrl，而不是签名上传URL
      return { url: data.url, publicUrl: data.publicUrl, file }
    } catch (error) {
      console.error("Error getting presigned URL:", error)
      throw error
    }
  }

  // 上传视频到R2
  const uploadVideoToR2 = async (presignedData: { url: string, publicUrl: string, file: File }) => {
    try {
      // 使用签名URL执行实际上传
      const response = await fetch(presignedData.url, {
        method: "PUT",
        body: presignedData.file,
        headers: {
          "Content-Type": presignedData.file.type,
        },
      })

      if (!response.ok) {
        throw new Error("Failed to upload file")
      }

      // 返回公开URL，这是最终要发送给后端的
      return presignedData.publicUrl
    } catch (error) {
      console.error("Error uploading to R2:", error)
      throw error
    }
  }

  // 执行上传所有视频
  const uploadAllVideos = async () => {
    if (videos.length === 0) {
      return false
    }

    if (isUploading) {
      return false
    }

    try {
      let uploadedCount = 0;
      const totalToUpload = videos.length;
      console.log(`准备上传 ${totalToUpload} 个视频...`);

      // 使用Promise.all并行上传所有视频
      const uploadTasks = videos.map(async (video) => {
        try {
          // 检查视频是否已有URL (预签名已经完成)
          if (!video.url) {
            console.log(`视频 ${video.id} 没有预签名URL，跳过上传`);
            return false;
          }

          // 获取文件和预签名URL
          const presignedUrl = video.url;

          // 获取实际的上传URL (要重新获取完整的上传URL)
          const response = await fetch("/api/train/upload", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              filename: video.file.name,
              contentType: video.file.type,
              size: video.file.size,
            }),
          });

          if (!response.ok) {
            throw new Error("Failed to get upload URL");
          }

          const { url, publicUrl } = await response.json();

          // 确保存储的URL与后端期望的URL一致
          if (presignedUrl !== publicUrl) {
            console.log(`警告：存储的URL(${presignedUrl})与新生成的publicUrl(${publicUrl})不一致`);
            // 更新为正确的公开URL
            updateVideoUrl(video.id, publicUrl);
          }

          // 实际上传到Cloudflare
          console.log(`上传视频: ${video.file.name}`);
          const uploadResponse = await fetch(url, {
            method: "PUT",
            body: video.file,
            headers: {
              "Content-Type": video.file.type,
            },
          });

          if (!uploadResponse.ok) {
            throw new Error("Failed to upload video");
          }

          console.log(`视频上传成功: ${publicUrl}`);

          uploadedCount++;
          return true;
        } catch (error) {
          console.error(`视频 ${video.id} 上传失败:`, error);
          return false;
        }
      });

      // 等待所有上传任务完成
      const results = await Promise.all(uploadTasks);

      // 确保至少有一个视频成功上传
      const hasSuccessfulUploads = results.some(result => result);

      if (!hasSuccessfulUploads) {
        toast.error("Upload Failed", "Failed to upload any videos");
        setIsUploading(false);
        return false;
      }

      console.log(`成功上传 ${uploadedCount} 个视频，共 ${totalToUpload} 个`);
      return true;
    } catch (error) {
      console.error("上传过程中发生错误:", error);
      const message = error instanceof Error ? error.message : "Failed to upload videos";
      toast.error("Upload Failed", message);
      return false;
    } finally {
      setIsUploading(false);
    }
  }

  // 向父组件暴露uploadAllVideos方法
  useEffect(() => {
    if (window) {
      window.uploadTrainingVideos = uploadAllVideos
    }

    return () => {
      if (window && window.uploadTrainingVideos) {
        delete window.uploadTrainingVideos
      }
    }
  }, [videos, uploadAllVideos, toast, updateVideoUrl])

  // 开始预上传单个视频（获取预签名URL但不实际上传）
  const prepareVideoUpload = async (video: { file: File; id: string }) => {
    try {
      // 标记该视频正在准备中
      setPreparingVideos(prev => {
        const updated = new Set(prev);
        updated.add(video.id);
        return updated;
      });

      // 获取预签名URL
      const presignedData = await getPresignedUrl(video.file);
      // 存储预签名信息到视频对象中
      updateVideoUrl(video.id, presignedData.publicUrl);

      // 完成后移除标记
      setPreparingVideos(prev => {
        const updated = new Set(prev);
        updated.delete(video.id);
        return updated;
      });

      return true;
    } catch (error) {
      console.error("Error preparing video upload:", error);
      // 发生错误时也移除标记
      setPreparingVideos(prev => {
        const updated = new Set(prev);
        updated.delete(video.id);
        return updated;
      });
      return false;
    }
  }

  // Handle video selection (with immediate URL preparation)
  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      // 阻止过多上传
      if (acceptedFiles.length === 0) return;

      // Check if adding these files would exceed the size limit
      const newTotalSize = acceptedFiles.reduce((sum, file) => sum + file.size, totalSize)

      if (newTotalSize > MAX_SIZE_BYTES) {
        toast.error("Upload Failed", "Total video size cannot exceed 1GB")
        return
      }

      // 创建视频预览
      const newVideos = acceptedFiles.map((file) => ({
        file,
        id: Math.random().toString(36).substring(2, 11),
        preview: URL.createObjectURL(file),
      }))

      // 添加视频到状态
      addVideos(newVideos)
      updateTotalSize(newTotalSize)

      // 静默在后台为每个新添加的视频获取预签名URL
      newVideos.forEach(video => {
        prepareVideoUpload(video).catch(err => {
          console.error(`Background preparation failed for video ${video.id}:`, err);
        });
      });
    },
    [totalSize, addVideos, updateTotalSize, toast, updateVideoUrl],
  )

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "video/*": [".mp4", ".mov", ".avi", ".mkv"],
    },
    maxSize: MAX_SIZE_BYTES,
  })

  // Clean up object URLs when component unmounts
  useEffect(() => {
    return () => {
      videos.forEach((video) => URL.revokeObjectURL(video.preview))
    }
  }, [videos])

  return (
    <div className="space-y-3 flex flex-col h-full">
      <div className="cyber-card pixel-border rounded-xl p-4 shadow-xl flex-1 flex flex-col">
        <div className="flex justify-between items-center">
          {/* Progress bar with neon effect */}
          <div className={cn(
            "h-2 w-full rounded-full overflow-hidden border",
            isDarkTheme
              ? "bg-[#111] border-[#F5EFFF]/30"
              : "bg-gray-200 border-black/30"
          )}>
            <div
              className={cn(
                "h-full rounded-full relative",
                isDarkTheme
                  ? "bg-gradient-to-r from-[#F5EFFF] to-[#00ffaa]"
                  : "bg-gradient-to-r from-blue-500 to-green-500"
              )}
              style={{ width: `${Math.min(100, (totalSize / MAX_SIZE_BYTES) * 100)}%` }}
            >
              <div className="absolute inset-0 bg-[linear-gradient(90deg,transparent_0%,rgba(0,255,0,0.4)_50%,transparent_100%)] animate-shimmer-slide"></div>
            </div>
          </div>
          {/* Terminal style header */}
          <div className="flex justify-between items-center flex-shrink-0 ml-5 pr-3">
            <p className={cn(
              "text-sm font-mono",
              isDarkTheme ? "text-[#F5EFFF]" : "text-black"
            )}>
              <span className="opacity-70">SIZE:</span> {formatBytes(totalSize)}<span className="opacity-70">/1GB</span>
            </p>
          </div>
        </div>

        {/* 只有在执行训练上传时才显示全屏loading弹窗 */}
        {isUploading && (
          <div className="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-50">
            <div className="flex flex-col items-center justify-center space-y-4">
              <div className="relative">
                <div className={cn(
                  "w-20 h-20 rounded-full border-4",
                  isDarkTheme
                    ? "border-[#F5EFFF]/20 border-t-[#F5EFFF]"
                    : "border-blue-200 border-t-blue-500"
                )} style={{ animation: "spin 1s linear infinite" }}></div>
                <div className={cn(
                  "absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-16 h-16 rounded-full border-4",
                  isDarkTheme
                    ? "border-[#00ffaa]/20 border-b-[#00ffaa]"
                    : "border-green-200 border-b-green-500"
                )} style={{ animation: "spin 2s linear infinite reverse" }}></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                  <Loader2 className={cn(
                    "w-10 h-10 animate-pulse",
                    isDarkTheme ? "text-[#F5EFFF]" : "text-blue-500"
                  )} />
                </div>
              </div>
              <div className={cn(
                "font-mono tracking-widest text-center",
                isDarkTheme ? "text-[#F5EFFF]" : "text-white"
              )}>
                <p className="text-lg uppercase animate-pulse">Uploading</p>
                <p className="text-xs mt-1">TRANSMITTING VIDEO DATA TO CLOUDFLARE</p>
              </div>
              <div className="flex space-x-1 mt-2">
                {[1, 2, 3, 4].map((i) => (
                  <div
                    key={i}
                    className={cn(
                      "w-2 h-2 rounded-full",
                      isDarkTheme ? "bg-[#F5EFFF]" : "bg-white"
                    )}
                    style={{ animation: `pulse 1.5s infinite ${i * 0.2}s` }}
                  ></div>
                ))}
              </div>
            </div>
          </div>
        )}

        {videos.length > 0 && (
          <div className="grid grid-cols-[repeat(auto-fill,minmax(200px,1fr))] gap-4 auto-rows-[200px] flex-1 overflow-y-auto pr-1 mt-4">
            {/* Add videos card */}
            <AddVideoCard
              getRootProps={getRootProps}
              getInputProps={getInputProps}
              isDragActive={isDragActive}
            />

            {/* Video preview cards */}
            {videos.map((video) => (
              <VideoCard key={video.id} video={video} isPreparing={preparingVideos.has(video.id)} />
            ))}
          </div>
        )}

        {videos.length === 0 && (
          <div
            className={cn(
              "border-2 border-dashed rounded-lg p-12 flex flex-col items-center justify-center hover:border-opacity-80 transition-colors duration-300 cursor-pointer flex-1 my-2",
              isDarkTheme
                ? "border-[#F5EFFF]/30 hover:border-[#F5EFFF] bg-[#000]/50"
                : "border-black/30 hover:border-black bg-gray-50"
            )}
            {...getRootProps()}
          >
            <input {...getInputProps()} />
            <div className={cn(
              "p-4 rounded-full mb-4 animate-pulse",
              isDarkTheme
                ? "bg-[#F5EFFF]/10 text-[#F5EFFF]"
                : "bg-black/10 text-black"
            )}>
              <FileVideo className="h-8 w-8" />
            </div>
            <p className={cn(
              "text-center mb-2 font-mono uppercase tracking-wider",
              isDarkTheme
                ? "text-[#F5EFFF]"
                : "text-black font-bold"
            )}>
              DRAG AND DROP VIDEOS <span className="blink-cursor">_</span>
            </p>
            <p className={cn(
              "text-xs text-center font-mono",
              isDarkTheme
                ? "text-[#F5EFFF]/70"
                : "text-black/70"
            )}>
              FORMAT: MP4, MOV, AVI, MKV
            </p>
            <p className={cn(
              "text-xs text-center font-mono mt-2",
              isDarkTheme
                ? "text-[#F5EFFF]/70"
                : "text-black/70"
            )}>
              Videos will be uploaded when training is executed
            </p>
          </div>
        )}
      </div>
    </div>
  )
}

