import { Share } from "lucide-react"
import { cn } from "@/lib/utils"
import { copyToClipboard } from "@/lib/utils"
import { useToast } from "@/hooks/use-sonner-toast"
import { motion } from "framer-motion"

interface CardShareButtonProps {
    isHovering: boolean
    isExpanded: boolean
    postId: string
    onClick: (e: React.MouseEvent) => void
}

export function CardShareButton({
    postId,
    onClick
}: CardShareButtonProps) {
    const { toast } = useToast()

    const handleShare = async (e: React.MouseEvent) => {
        e.preventDefault()
        e.stopPropagation()

        // 构建分享链接
        const shareUrl = `${window.location.origin}/posts/${postId}`

        // 尝试复制到剪贴板
        const success = await copyToClipboard(shareUrl)

        if (success) {
            toast({
                type: "success",
                title: "Copied",
                message: "Video link copied to clipboard"
            })
        } else {
            toast({
                type: "error",
                title: "Failed to copy",
                message: "Unable to copy link to clipboard"
            })
        }

        // 调用外部传入的onClick回调
        onClick(e)
    }

    return (
        <motion.button
            onClick={handleShare}
            whileTap={{ scale: 0.95 }}
            whileHover={{
                boxShadow: "0 0 8px rgba(45, 132, 199, 0.3)"
            }}
            className={cn(
                "relative z-10 flex items-center justify-center overflow-hidden w-full",
                "transition-colors duration-300 ease-out",
                "bg-black/60 backdrop-blur-sm text-white/90",
                "transform-gpu",
                "rounded-full px-3 py-2 gap-1.5",
                "border border-rose-500/30"
            )}
        >
            {/* 图标 */}
            <Share size={16} className="text-rose-300" />

            {/* 文字 */}
            <span className="font-medium text-sm whitespace-nowrap">
                Share
            </span>

            {/* 淡雅的边框效果 */}
            <motion.div
                className="absolute inset-0 -z-10 rounded-full bg-gradient-to-r from-rose-500/10 to-rose-600/20"
                initial={{ opacity: 0.15 }}
                whileHover={{ opacity: 0.4 }}
                transition={{ duration: 0.2 }}
            />
        </motion.button>
    )
} 