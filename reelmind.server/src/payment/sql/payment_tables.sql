-- 支付表结构
create table public.payments (
  id uuid not null default gen_random_uuid (),
  user_id uuid not null,
  amount numeric(10, 2) not null,
  payment_type text not null, -- 支付类型：'subscription'（首次会员订阅）, 'subscription_renewal'（会员续费）, 'credits_purchase'（积分充值）, 'other'（其他）
  status text not null,
  subscription_id text null, -- 仅用于会员订阅类型
  plan_id uuid null, -- 仅用于会员订阅类型
  metadata jsonb null,
  details jsonb null,
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone not null default now(),
  constraint payments_pkey primary key (id),
  constraint payments_user_id_fkey foreign KEY (user_id) references auth.users (id) on delete CASCADE,
  constraint payments_plan_id_fkey foreign KEY (plan_id) references public.membership_plans (id)
) TABLESPACE pg_default;

create index IF not exists idx_payments_user_id on public.payments using btree (user_id) TABLESPACE pg_default;
create index IF not exists idx_payments_subscription_id on public.payments using btree (subscription_id) TABLESPACE pg_default;
create index IF not exists idx_payments_plan_id on public.payments using btree (plan_id) TABLESPACE pg_default;
create index IF not exists idx_payments_payment_type on public.payments using btree (payment_type) TABLESPACE pg_default;
create index IF not exists idx_payments_status on public.payments using btree (status) TABLESPACE pg_default;