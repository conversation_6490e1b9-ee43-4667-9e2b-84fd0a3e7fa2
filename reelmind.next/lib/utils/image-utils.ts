/**
 * Image validation and compression utilities
 */

// Supported image formats
export const SUPPORTED_IMAGE_FORMATS = ['image/png', 'image/jpeg', 'image/jpg'] as const;
export const SUPPORTED_IMAGE_EXTENSIONS = ['.png', '.jpg', '.jpeg'] as const;

// Maximum file size (10MB)
export const MAX_IMAGE_SIZE = 10 * 1024 * 1024;

/**
 * Validate if the file is a supported image format
 */
export function validateImageFormat(file: File): { isValid: boolean; error?: string } {
  // Check MIME type
  if (!SUPPORTED_IMAGE_FORMATS.includes(file.type as any)) {
    return {
      isValid: false,
      error: `Unsupported image format. Only PNG, JPG, and JPEG files are allowed.`
    };
  }

  // Additional check for file extension
  const fileName = file.name.toLowerCase();
  const hasValidExtension = SUPPORTED_IMAGE_EXTENSIONS.some(ext => fileName.endsWith(ext));
  
  if (!hasValidExtension) {
    return {
      isValid: false,
      error: `Invalid file extension. Only .png, .jpg, and .jpeg files are allowed.`
    };
  }

  return { isValid: true };
}

/**
 * Compress image to target size if it exceeds the limit
 */
export function compressImage(file: File, maxSize: number = MAX_IMAGE_SIZE): Promise<File> {
  return new Promise((resolve, reject) => {
    // If file is already under the size limit, return as is
    if (file.size <= maxSize) {
      resolve(file);
      return;
    }

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      try {
        // Set canvas dimensions to image dimensions
        canvas.width = img.width;
        canvas.height = img.height;

        // Draw image on canvas
        ctx?.drawImage(img, 0, 0);

        // Start with high quality and reduce until we meet size requirements
        let quality = 0.9;
        let compressedBlob: Blob | null = null;

        const tryCompress = () => {
          canvas.toBlob((blob) => {
            if (!blob) {
              reject(new Error('Failed to compress image'));
              return;
            }

            // If we've reached the target size or minimum quality, use this result
            if (blob.size <= maxSize || quality <= 0.1) {
              compressedBlob = blob;
              
              // Create new File object with compressed data
              const compressedFile = new File([compressedBlob], file.name, {
                type: file.type,
                lastModified: Date.now(),
              });
              
              resolve(compressedFile);
              return;
            }

            // Reduce quality and try again
            quality -= 0.1;
            tryCompress();
          }, file.type, quality);
        };

        tryCompress();
      } catch (error) {
        reject(new Error('Failed to process image for compression'));
      }
    };

    img.onerror = () => {
      reject(new Error('Failed to load image for compression'));
    };

    // Load the image
    img.src = URL.createObjectURL(file);
  });
}

/**
 * Validate and compress image if needed
 */
export async function processImageFile(file: File): Promise<{ 
  processedFile: File; 
  wasCompressed: boolean; 
  originalSize: number; 
  finalSize: number; 
}> {
  // First validate the format
  const formatValidation = validateImageFormat(file);
  if (!formatValidation.isValid) {
    throw new Error(formatValidation.error);
  }

  const originalSize = file.size;
  let wasCompressed = false;

  // Compress if needed
  let processedFile = file;
  if (file.size > MAX_IMAGE_SIZE) {
    processedFile = await compressImage(file, MAX_IMAGE_SIZE);
    wasCompressed = true;
  }

  return {
    processedFile,
    wasCompressed,
    originalSize,
    finalSize: processedFile.size,
  };
}

/**
 * Format file size for display
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
