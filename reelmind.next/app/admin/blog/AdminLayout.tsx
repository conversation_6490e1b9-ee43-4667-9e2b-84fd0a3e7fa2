"use client";

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import useAuthStore from '@/store/useAuthStore';
import { userApi } from '@/lib/api/user';
import { useToast } from '@/hooks/use-sonner-toast';
import { cn } from '@/lib/utils';
import { BookOpen, MessageSquare, Settings, BarChart3, Users, ListTodo, Coins, TrendingUp } from 'lucide-react';

interface AdminLayoutProps {
    children: React.ReactNode;
}

// 管理员导航项
const adminNavItems = [
    {
        href: '/admin/blog',
        label: '博客管理',
        icon: BookOpen,
    },
    {
        href: '/admin/feedbacks',
        label: '反馈管理',
        icon: MessageSquare,
    },
    {
        href: '/admin/memberships',
        label: '订阅管理',
        icon: Users,
    },
    {
        href: '/admin/models',
        label: '模型管理',
        icon: Settings,
    },
    {
        href: '/admin/tasks',
        label: '任务管理',
        icon: ListTodo,
    },
    {
        href: '/admin/credits',
        label: '积分管理',
        icon: Coins,
    },
    {
        href: '/admin/stats',
        label: '增长统计',
        icon: TrendingUp,
    },
];

export function AdminLayout({ children }: AdminLayoutProps) {
    const router = useRouter();
    const pathname = usePathname();
    const { isAuthenticated, isLoading, checkAuthStatus } = useAuthStore();
    const [isAdmin, setIsAdmin] = React.useState<boolean | null>(null);
    const [isChecking, setIsChecking] = React.useState(true);
    const { error } = useToast();

    // 检查用户是否为管理员
    useEffect(() => {
        const checkAdminStatus = async () => {
            if (!isAuthenticated) {
                await checkAuthStatus();
            };

            setIsChecking(true);
            try {
                // 获取用户角色
                const { role } = await userApi.getUserRole();
                const hasAdminRole = role === 'admin';
                setIsAdmin(hasAdminRole);

                if (!hasAdminRole) {
                    error('访问被拒绝', '无权访问管理页面');
                    router.push('/');
                }
            } catch (err) {
                console.error('检查管理员权限失败:', err);
                error('权限验证', '权限验证失败');
                router.push('/');
            } finally {
                setIsChecking(false);
            }
        };

        checkAdminStatus();
    }, [isAuthenticated]);

    // 显示加载状态
    if (isLoading || isChecking) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
                    <p className="mt-4 text-gray-600 dark:text-gray-300">加载中...</p>
                </div>
            </div>
        );
    }

    // 如果用户未登录或非管理员，路由中间件会处理重定向
    // 这里只是额外的客户端保护
    if (!isAuthenticated || isAdmin === false) {
        return null;
    }

    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
            <div className="flex">
                {/* 侧边栏 */}
                <div className="w-64 bg-white dark:bg-gray-800 shadow-sm border-r border-gray-200 dark:border-gray-700 min-h-screen">
                    <div className="p-6">
                        <Link href="/admin" className="flex items-center space-x-2">
                            <BarChart3 className="w-8 h-8 text-blue-500" />
                            <span className="text-xl font-bold text-gray-900 dark:text-white">
                                管理中心
                            </span>
                        </Link>
                    </div>

                    <nav className="mt-6">
                        <div className="px-6 mb-2">
                            <h3 className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                                管理功能
                            </h3>
                        </div>
                        <ul className="space-y-1 px-3">
                            {adminNavItems.map((item) => {
                                const IconComponent = item.icon;
                                const isActive = pathname?.startsWith(item.href);

                                return (
                                    <li key={item.href}>
                                        <Link
                                            href={item.href}
                                            className={cn(
                                                'flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
                                                isActive
                                                    ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-200'
                                                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                                            )}
                                        >
                                            <IconComponent className="w-5 h-5 mr-3" />
                                            {item.label}
                                        </Link>
                                    </li>
                                );
                            })}
                        </ul>
                    </nav>
                </div>

                {/* 主内容区域 */}
                <div className="flex-1">
                    <main className="p-8">
                        {children}
                    </main>
                </div>
            </div>
        </div>
    );
}