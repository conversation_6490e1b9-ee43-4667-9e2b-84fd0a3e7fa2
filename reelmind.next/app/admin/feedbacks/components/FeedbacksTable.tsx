"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { formatDate } from '@/lib/utils';
import { useToast } from '@/hooks/use-sonner-toast';

// 反馈类型枚举
export enum FeedbackType {
    BUG = 'bug',
    SUGGESTION = 'suggestion',
    OTHER = 'other'
}

// 反馈数据接口
export interface FeedbackData {
    id: string;
    user_id: string;
    type: FeedbackType;
    subject: string;
    message: string;
    created_at: string;
    user_email?: string;
    user_nickname?: string;
}

// 反馈响应接口
interface FeedbacksResponse {
    feedbacks: FeedbackData[];
    total: number;
    page: number;
    limit: number;
}

interface FeedbacksTableProps {
    initialPage: number;
    initialType: string;
}

export default function FeedbacksTable({ initialPage, initialType }: FeedbacksTableProps) {
    const [feedbacks, setFeedbacks] = useState<FeedbackData[]>([]);
    const [loading, setLoading] = useState(true);
    const [total, setTotal] = useState(0);
    const [page, setPage] = useState(initialPage);
    const [type, setType] = useState(initialType);
    const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());
    const { error, success } = useToast();

    const limit = 20;

    // 获取反馈数据
    const fetchFeedbacks = async () => {
        setLoading(true);
        try {
            const params = new URLSearchParams({
                page: page.toString(),
                limit: limit.toString(),
            });

            if (type) {
                params.append('type', type);
            }

            const response = await fetch(`/api/admin/feedbacks?${params.toString()}`);

            if (!response.ok) {
                throw new Error('Failed to fetch feedbacks');
            }

            const result = await response.json();

            if (result.code === 200 && result.data) {
                setFeedbacks(result.data.feedbacks);
                setTotal(result.data.total);
            } else {
                throw new Error(result.message || 'Failed to fetch feedbacks');
            }
        } catch (err) {
            console.error('获取反馈数据失败:', err);
            console.error('Error details:', err);
            error('获取失败', `无法获取反馈数据: ${err instanceof Error ? err.message : '未知错误'}`);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchFeedbacks();
    }, [page, type]);

    // 切换行展开状态
    const toggleRowExpanded = (id: string) => {
        const newExpanded = new Set(expandedRows);
        if (newExpanded.has(id)) {
            newExpanded.delete(id);
        } else {
            newExpanded.add(id);
        }
        setExpandedRows(newExpanded);
    };

    // 获取反馈类型显示文本
    const getTypeText = (type: FeedbackType) => {
        switch (type) {
            case FeedbackType.BUG:
                return 'Bug报告';
            case FeedbackType.SUGGESTION:
                return '建议';
            case FeedbackType.OTHER:
                return '其他';
            default:
                return type;
        }
    };

    // 获取反馈类型样式
    const getTypeStyle = (type: FeedbackType) => {
        switch (type) {
            case FeedbackType.BUG:
                return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
            case FeedbackType.SUGGESTION:
                return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
            case FeedbackType.OTHER:
                return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
            default:
                return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
        }
    };

    // 计算分页信息
    const totalPages = Math.ceil(total / limit);
    const showPagination = totalPages > 1;

    if (loading) {
        return (
            <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
                <span className="ml-2 text-gray-600 dark:text-gray-300">加载中...</span>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* 筛选器 */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4">
                <div className="flex flex-wrap gap-4 items-center">
                    <div>
                        <label htmlFor="type-filter" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            反馈类型
                        </label>
                        <select
                            id="type-filter"
                            value={type}
                            onChange={(e) => {
                                setType(e.target.value);
                                setPage(1);
                            }}
                            className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                        >
                            <option value="">全部类型</option>
                            <option value={FeedbackType.BUG}>Bug报告</option>
                            <option value={FeedbackType.SUGGESTION}>建议</option>
                            <option value={FeedbackType.OTHER}>其他</option>
                        </select>
                    </div>
                    <div className="flex-1"></div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                        总计: {total} 条反馈
                    </div>
                </div>
            </div>

            {/* 反馈表格 */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                {feedbacks.length === 0 ? (
                    <div className="text-center py-12">
                        <div className="text-gray-500 dark:text-gray-400">
                            暂无反馈数据
                        </div>
                    </div>
                ) : (
                    <div className="overflow-x-auto">
                        <table className="w-full">
                            <thead className="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        用户信息
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        类型
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        主题
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        提交时间
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                        操作
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
                                {feedbacks.map((feedback) => (
                                    <React.Fragment key={feedback.id}>
                                        <tr className="hover:bg-gray-50 dark:hover:bg-gray-700">
                                            <td className="px-6 py-4">
                                                <div className="text-sm">
                                                    <div className="font-medium text-gray-900 dark:text-white">
                                                        {feedback.user_nickname || feedback.user_email || '未知用户'}
                                                    </div>
                                                    <div className="text-gray-500 dark:text-gray-400 text-xs">
                                                        {feedback.user_email || '无邮箱'}
                                                    </div>
                                                    <div className="text-gray-500 dark:text-gray-400 font-mono text-xs">
                                                        {feedback.user_id}
                                                    </div>
                                                </div>
                                            </td>
                                            <td className="px-6 py-4">
                                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getTypeStyle(feedback.type)}`}>
                                                    {getTypeText(feedback.type)}
                                                </span>
                                            </td>
                                            <td className="px-6 py-4">
                                                <div className="text-sm text-gray-900 dark:text-white max-w-xs truncate">
                                                    {feedback.subject}
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                                                {formatDate(feedback.created_at)}
                                            </td>
                                            <td className="px-6 py-4 text-sm font-medium">
                                                <button
                                                    onClick={() => toggleRowExpanded(feedback.id)}
                                                    className="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300"
                                                >
                                                    {expandedRows.has(feedback.id) ? '收起' : '查看详情'}
                                                </button>
                                            </td>
                                        </tr>
                                        {expandedRows.has(feedback.id) && (
                                            <tr>
                                                <td colSpan={5} className="px-6 py-4 bg-gray-50 dark:bg-gray-700">
                                                    <div className="space-y-3">
                                                        <div>
                                                            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                                                                反馈内容:
                                                            </h4>
                                                            <div className="bg-white dark:bg-gray-800 p-4 rounded-md border border-gray-200 dark:border-gray-600">
                                                                <p className="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                                                                    {feedback.message}
                                                                </p>
                                                            </div>
                                                        </div>
                                                        <div className="flex justify-end">
                                                            <button
                                                                onClick={() => toggleRowExpanded(feedback.id)}
                                                                className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200"
                                                            >
                                                                收起
                                                            </button>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        )}
                                    </React.Fragment>
                                ))}
                            </tbody>
                        </table>
                    </div>
                )}
            </div>

            {/* 分页 */}
            {showPagination && (
                <div className="flex justify-center">
                    <nav className="flex items-center gap-1">
                        {page > 1 && (
                            <Link
                                href={{ pathname: '/admin/feedbacks', query: { page: page - 1, ...(type && { type }) } }}
                                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-gray-100 rounded-md hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700"
                                onClick={() => setPage(page - 1)}
                            >
                                上一页
                            </Link>
                        )}

                        {Array.from({ length: Math.min(5, totalPages) }).map((_, i) => {
                            let pageNum;
                            if (totalPages <= 5) {
                                pageNum = i + 1;
                            } else if (page <= 3) {
                                pageNum = i + 1;
                            } else if (page >= totalPages - 2) {
                                pageNum = totalPages - 4 + i;
                            } else {
                                pageNum = page - 2 + i;
                            }

                            const isCurrentPage = pageNum === page;

                            return (
                                <Link
                                    key={i}
                                    href={{ pathname: '/admin/feedbacks', query: { page: pageNum, ...(type && { type }) } }}
                                    className={`px-4 py-2 text-sm font-medium rounded-md ${isCurrentPage
                                        ? 'bg-primary-600 text-white'
                                        : 'text-gray-700 dark:text-gray-200 bg-gray-100 hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700'
                                        }`}
                                    onClick={() => setPage(pageNum)}
                                >
                                    {pageNum}
                                </Link>
                            );
                        })}

                        {page < totalPages && (
                            <Link
                                href={{ pathname: '/admin/feedbacks', query: { page: page + 1, ...(type && { type }) } }}
                                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-gray-100 rounded-md hover:bg-gray-200 dark:bg-gray-800 dark:hover:bg-gray-700"
                                onClick={() => setPage(page + 1)}
                            >
                                下一页
                            </Link>
                        )}
                    </nav>
                </div>
            )}
        </div>
    );
} 