import { useState, useCallback } from 'react';
import { generationApi, VideoGenerationRequest } from "@/lib/api/generation";
import { useToast } from "@/hooks/use-sonner-toast";
import type { APIDuration, APIRatio } from "@/types/video";
import type { Model } from "@/types/model";
import useVideoGeneratorStore from "@/store/useVideoGeneratorStore";
import { processImageFile, formatFileSize } from '@/lib/utils/image-utils';
import { IMAGE_CONSTRAINTS } from "@/constants/upload";

export interface StartToEndModelHook {
  // State
  startImages: string[];
  endImages: string[];
  isUploading: boolean;
  isStartImageUploading: boolean;
  isEndImageUploading: boolean;

  // Actions
  handleStartImageUpload: (file: File) => Promise<void>;
  handleEndImageUpload: (file: File) => Promise<void>;
  removeStartImage: () => void;
  removeEndImage: () => void;
  generateStartToEndVideo: (model: Model, selectedEffect?: any) => Promise<any>;

  // External setters for remix functionality
  setStartImages: (images: string[]) => void;
  setEndImages: (images: string[]) => void;

  // Validation
  validateStartToEndModel: () => { isValid: boolean; errorMessage?: string };
}

export const useStartToEndModel = (
  setImageSizeError: (error: string | null) => void
): StartToEndModelHook => {
  const [startImages, setStartImages] = useState<string[]>([]);
  const [endImages, setEndImages] = useState<string[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [isStartImageUploading, setIsStartImageUploading] = useState(false);
  const [isEndImageUploading, setIsEndImageUploading] = useState(false);

  const { error: toastError, warning } = useToast();

  // Get store state
  const {
    prompt,
    negativePrompt,
    steps,
    seed,
    creativityLevel,
    duration,
    aspectRatio,
    selectedEffectName
  } = useVideoGeneratorStore();

  // Upload image helper function
  const uploadImage = useCallback(async (file: File, setUploadingState: (uploading: boolean) => void): Promise<string | null> => {
    // 处理图片文件：验证格式并压缩
    let processedFile: File;
    try {
      const result = await processImageFile(file);
      processedFile = result.processedFile;

      // 如果图片被压缩，显示提示信息
      if (result.wasCompressed) {
        warning("Image Compressed", `Image was compressed from ${formatFileSize(result.originalSize)} to ${formatFileSize(result.finalSize)} to meet the 10MB limit.`);
      }
    } catch (error) {
      toastError("File Format Error", error instanceof Error ? error.message : "Invalid image format");
      return null;
    }

    // 检查图片尺寸
    const checkImageDimensions = (): Promise<boolean> => {
      return new Promise((resolve) => {
        const img = new Image();
        img.onload = () => {
          // 检查最小尺寸
          if (img.width < IMAGE_CONSTRAINTS.MIN_WIDTH || img.height < IMAGE_CONSTRAINTS.MIN_HEIGHT) {
            setImageSizeError(`Image dimensions must be at least ${IMAGE_CONSTRAINTS.MIN_WIDTH}×${IMAGE_CONSTRAINTS.MIN_HEIGHT} pixels (current: ${img.width}×${img.height})`);
            resolve(false);
            return;
          }

          // 检查比例限制
          const aspectRatio = img.width / img.height;

          if (aspectRatio > IMAGE_CONSTRAINTS.MAX_ASPECT_RATIO || aspectRatio < IMAGE_CONSTRAINTS.MIN_ASPECT_RATIO) {
            const currentRatio = aspectRatio.toFixed(2);
            setImageSizeError(`Image aspect ratio must be between ${IMAGE_CONSTRAINTS.MIN_ASPECT_RATIO} and ${IMAGE_CONSTRAINTS.MAX_ASPECT_RATIO} (current: ${currentRatio})`);
            resolve(false);
            return;
          }

          resolve(true);
        };
        img.onerror = () => {
          setImageSizeError("Failed to load image for dimension validation");
          resolve(false);
        };
        img.src = URL.createObjectURL(processedFile);
      });
    };

    const isValidSize = await checkImageDimensions();
    if (!isValidSize) {
      toastError("Image Size Error", "Image size validation failed");
      return null;
    }

    // 上传到服务器
    setUploadingState(true);
    setIsUploading(true);

    try {
      const response = await fetch('/api/upload/presign', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          filename: processedFile.name,
          fileType: processedFile.type,
          fileSize: processedFile.size,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        toastError("Upload Error", errorData.message || 'Failed to get upload URL');
        return null;
      }

      const { data } = await response.json();

      const uploadResponse = await fetch(data.url, {
        method: 'PUT',
        headers: {
          'Content-Type': processedFile.type,
        },
        body: processedFile,
      });

      if (!uploadResponse.ok) {
        toastError("Upload Error", "Upload image failed");
        return null;
      }

      return data.publicUrl;
    } catch (error) {
      console.error('Upload error:', error);
      toastError("Upload Error", "Upload failed, please try again");
      return null;
    } finally {
      setUploadingState(false);
      setIsUploading(false);
    }
  }, [setImageSizeError, toastError, setIsUploading]);

  // Handle start image upload
  const handleStartImageUpload = useCallback(async (file: File) => {
    setImageSizeError(null);

    // Show preview immediately
    const reader = new FileReader();
    reader.onload = (event) => {
      if (event.target?.result) {
        setStartImages([event.target.result as string]);
      }
    };
    reader.readAsDataURL(file);

    // Upload to server
    const publicUrl = await uploadImage(file, setIsStartImageUploading);
    if (publicUrl) {
      setStartImages([publicUrl]);
    } else {
      // If upload failed, clear the preview
      setStartImages([]);
    }
  }, [uploadImage, setImageSizeError]);

  // Handle end image upload
  const handleEndImageUpload = useCallback(async (file: File) => {
    setImageSizeError(null);

    // Show preview immediately
    const reader = new FileReader();
    reader.onload = (event) => {
      if (event.target?.result) {
        setEndImages([event.target.result as string]);
      }
    };
    reader.readAsDataURL(file);

    // Upload to server
    const publicUrl = await uploadImage(file, setIsEndImageUploading);
    if (publicUrl) {
      setEndImages([publicUrl]);
    } else {
      // If upload failed, clear the preview
      setEndImages([]);
    }
  }, [uploadImage, setImageSizeError]);

  // Remove images
  const removeStartImage = useCallback(() => {
    setImageSizeError(null);
    setStartImages([]);
  }, [setImageSizeError]);

  const removeEndImage = useCallback(() => {
    setImageSizeError(null);
    setEndImages([]);
  }, [setImageSizeError]);

  // External setters for remix functionality
  const setStartImagesExternal = useCallback((images: string[]) => {
    setImageSizeError(null);
    setStartImages(images);
  }, [setImageSizeError]);

  const setEndImagesExternal = useCallback((images: string[]) => {
    setImageSizeError(null);
    setEndImages(images);
  }, [setImageSizeError]);

  // Validation
  const validateStartToEndModel = useCallback(() => {
    if (startImages.length === 0) {
      return { isValid: false, errorMessage: "Please upload a start frame image" };
    }

    if (endImages.length === 0) {
      return { isValid: false, errorMessage: "Please upload an end frame image" };
    }

    // Check if images are still uploading
    if (isStartImageUploading || isEndImageUploading) {
      return { isValid: false, errorMessage: "Images are uploading, please wait for upload to complete" };
    }

    // Check if images are still local previews (data URLs)
    if (startImages[0].startsWith('data:') || endImages[0].startsWith('data:')) {
      return { isValid: false, errorMessage: "Images are processing, please wait for upload to complete" };
    }

    return { isValid: true };
  }, [startImages, endImages, isStartImageUploading, isEndImageUploading]);

  // Generate video for start-to-end model
  const generateStartToEndVideo = useCallback(async (model: Model, selectedEffect?: any) => {
    try {
      useVideoGeneratorStore.getState().isGenerating = true;
      useVideoGeneratorStore.getState().error = null;

      // 生成随机种子（如果未提供）
      const usedSeed = seed.toString().trim() ? parseInt(seed.toString()) : Math.floor(Math.random() * 1000000);

      // 准备请求参数
      const params: VideoGenerationRequest = {
        model_id: model.id,
        prompt: prompt,
        negative_prompt: negativePrompt,
        steps: steps,
        seed: usedSeed,
        gen_type: model.model_type,
        guidance_scale: creativityLevel / 10,
        duration: duration as APIDuration,
        ratio: aspectRatio as APIRatio,
        // 首尾帧模型的特殊参数
        start_image_url: startImages[0],
        end_image_url: endImages[0],
      };

      // 如果选择了效果，添加效果名称到参数
      if (selectedEffectName) {
        params.effect_name = selectedEffectName;

        // 如果传入了效果对象，且有trigger_words，添加到请求参数
        if (selectedEffect && selectedEffect.trigger_words && selectedEffect.trigger_words.length > 0) {
          params.trigger_words = selectedEffect.trigger_words;
        }
      }

      // 调用API创建任务
      const task = await generationApi.createTask(params);

      // 更新状态
      useVideoGeneratorStore.getState().currentTask = task;
      useVideoGeneratorStore.getState().hasNewTask = true;

      return task;
    } catch (error) {
      // 检查是否为并发任务限制错误
      if (error instanceof Error) {
        const errorMessage = error.message;

        // 检查是否为并发限制错误
        if (errorMessage.includes("only have one video generation task running at a time") ||
          errorMessage.includes("Free users can only have one") ||
          errorMessage.includes("upgrade to premium for unlimited concurrent tasks")) {
          // 触发并发限制对话框显示事件
          window.dispatchEvent(new CustomEvent('showConcurrentLimitDialog', {
            detail: { message: errorMessage }
          }));

          return null;
        }

        // 其他错误正常处理
        useVideoGeneratorStore.getState().error = errorMessage;
      } else {
        useVideoGeneratorStore.getState().error = "An unknown error occurred while generating the video";
      }
      return null;
    } finally {
      useVideoGeneratorStore.getState().isGenerating = false;
    }
  }, [prompt, negativePrompt, steps, seed, creativityLevel, duration, aspectRatio, startImages, endImages, selectedEffectName]);

  return {
    startImages,
    endImages,
    isUploading,
    isStartImageUploading,
    isEndImageUploading,
    handleStartImageUpload,
    handleEndImageUpload,
    removeStartImage,
    removeEndImage,
    generateStartToEndVideo,
    setStartImages: setStartImagesExternal,
    setEndImages: setEndImagesExternal,
    validateStartToEndModel,
  };
};
