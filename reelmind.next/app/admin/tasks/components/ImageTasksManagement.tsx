"use client";

import React, { useState, useEffect } from 'react';
import { useToast } from '@/hooks/use-sonner-toast';
import { ImageTaskTable } from './ImageTaskTable';
import { ImageTaskFilters } from './ImageTaskFilters';
import { ImageTaskDetails } from './ImageTaskDetails';

interface ImageTask {
    id: string;
    user_id: string;
    status: 'pending' | 'processing' | 'completed' | 'failed';
    progress: number;
    input_params: any;
    output_result?: any;
    error_log?: any;
    created_at: string;
    started_at?: string;
    completed_at?: string;
    last_activity_at: string;
    model?: string;
    priority?: number;
    profiles?: {
        email: string;
        full_name: string;
    };
}

interface ImageTasksResponse {
    tasks: ImageTask[];
    total: number;
    page: number;
    limit: number;
}

interface Filters {
    status?: string;
    userId?: string;
    model?: string;
}

export function ImageTasksManagement() {
    const [tasks, setTasks] = useState<ImageTask[]>([]);
    const [loading, setLoading] = useState(true);
    const [total, setTotal] = useState(0);
    const [page, setPage] = useState(1);
    const [limit] = useState(20);
    const [filters, setFilters] = useState<Filters>({});
    const [selectedTaskId, setSelectedTaskId] = useState<string | null>(null);
    const { error } = useToast();

    // 获取任务列表
    const fetchTasks = async () => {
        try {
            setLoading(true);
            const params = new URLSearchParams({
                page: page.toString(),
                limit: limit.toString(),
                ...filters
            });

            const response = await fetch(`/api/admin/image-tasks?${params}`);
            const data: ImageTasksResponse = await response.json();

            if (!response.ok) {
                throw new Error(data.error || '获取任务列表失败');
            }

            setTasks(data.tasks);
            setTotal(data.total);
        } catch (err) {
            error('获取任务列表失败', err instanceof Error ? err.message : '未知错误');
        } finally {
            setLoading(false);
        }
    };

    // 处理过滤器变化
    const handleFiltersChange = (newFilters: Filters) => {
        setFilters(newFilters);
        setPage(1); // 重置到第一页
    };

    // 初始加载和依赖更新
    useEffect(() => {
        fetchTasks();
    }, [page, filters]);

    return (
        <div className="p-6 space-y-6">
            {/* 过滤器 */}
            <ImageTaskFilters
                filters={filters}
                onFiltersChange={handleFiltersChange}
                onRefresh={fetchTasks}
            />

            {/* 任务表格 */}
            <ImageTaskTable
                tasks={tasks}
                loading={loading}
                total={total}
                page={page}
                limit={limit}
                onPageChange={setPage}
                onViewDetails={setSelectedTaskId}
            />

            {/* 任务详情模态框 */}
            {selectedTaskId && (
                <ImageTaskDetails
                    taskId={selectedTaskId}
                    open={!!selectedTaskId}
                    onClose={() => setSelectedTaskId(null)}
                />
            )}
        </div>
    );
} 