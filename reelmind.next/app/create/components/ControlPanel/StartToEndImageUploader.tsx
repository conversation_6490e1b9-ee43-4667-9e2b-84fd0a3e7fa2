"use client"

import React from 'react';
import { Upload, X } from 'lucide-react';
import Image from 'next/image';
import { useControlPanel } from './ControlPanelContext';
import { IMAGE_CONSTRAINTS } from "@/constants/upload";

export const StartToEndImageUploader: React.FC = () => {
  const {
    startImages,
    endImages,
    startFileInputRef,
    endFileInputRef,
    handleStartImageUpload,
    handleEndImageUpload,
    removeStartImage,
    removeEndImage,
    isUploadingStartToEnd,
    isStartImageUploading,
    isEndImageUploading,
    imageSizeError
  } = useControlPanel();

  const handleStartUploadClick = () => {
    if (!isStartImageUploading) {
      startFileInputRef.current?.click();
    }
  };

  const handleEndUploadClick = () => {
    if (!isEndImageUploading) {
      endFileInputRef.current?.click();
    }
  };

  return (
    <div className="space-y-4">
      <div className="text-sm font-medium text-foreground mb-3">
        Start & End Frame Images
      </div>

      <div className="grid grid-cols-2 gap-4">
        {/* Start Image Upload */}
        <div className="space-y-2">
          <div className="text-xs text-muted-foreground text-center">Start Frame</div>
          <div className="max-w-[160px] mx-auto">
            <div className="h-[160px] aspect-square rounded-xl overflow-hidden">
              {startImages.length > 0 ? (
                <div className="relative w-full h-full">
                  <Image
                    src={startImages[0]}
                    alt="Start frame image"
                    fill
                    className={`object-cover ${imageSizeError ? 'border-2 border-red-500' : ''}`}
                  />
                  {isStartImageUploading && (
                    <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                      <div className="flex flex-col items-center text-white">
                        <div className="animate-spin w-6 h-6 rounded-full border-2 border-t-white border-white/30 mb-2"></div>
                        <span className="text-xs">Uploading...</span>
                      </div>
                    </div>
                  )}
                  <button
                    onClick={() => removeStartImage(0)}
                    className="absolute top-2 right-2 bg-black/50 hover:bg-black/70 text-white rounded-full p-1 transition-colors"
                    disabled={isUploadingStartToEnd || isStartImageUploading}
                  >
                    <X size={16} />
                  </button>
                </div>
              ) : (
                <div
                  onClick={handleStartUploadClick}
                  className={`w-full h-full border-2 border-dashed border-muted-foreground/30 rounded-xl flex flex-col items-center justify-center transition-colors bg-muted/20 ${isStartImageUploading
                    ? 'cursor-not-allowed opacity-50'
                    : 'cursor-pointer hover:border-muted-foreground/50'
                    }`}
                >
                  <Upload size={24} className="text-muted-foreground mb-2" />
                  <span className="text-xs text-muted-foreground text-center px-2">
                    Drop start image here
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* End Image Upload */}
        <div className="space-y-2">
          <div className="text-xs text-muted-foreground text-center">End Frame</div>
          <div className="max-w-[160px] mx-auto">
            <div className="h-[160px] aspect-square rounded-xl overflow-hidden">
              {endImages.length > 0 ? (
                <div className="relative w-full h-full">
                  <Image
                    src={endImages[0]}
                    alt="End frame image"
                    fill
                    className={`object-cover ${imageSizeError ? 'border-2 border-red-500' : ''}`}
                  />
                  {isEndImageUploading && (
                    <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                      <div className="flex flex-col items-center text-white">
                        <div className="animate-spin w-6 h-6 rounded-full border-2 border-t-white border-white/30 mb-2"></div>
                        <span className="text-xs">Uploading...</span>
                      </div>
                    </div>
                  )}
                  <button
                    onClick={() => removeEndImage(0)}
                    className="absolute top-2 right-2 bg-black/50 hover:bg-black/70 text-white rounded-full p-1 transition-colors"
                    disabled={isUploadingStartToEnd || isEndImageUploading}
                  >
                    <X size={16} />
                  </button>
                </div>
              ) : (
                <div
                  onClick={handleEndUploadClick}
                  className={`w-full h-full border-2 border-dashed border-muted-foreground/30 rounded-xl flex flex-col items-center justify-center transition-colors bg-muted/20 ${isEndImageUploading
                      ? 'cursor-not-allowed opacity-50'
                      : 'cursor-pointer hover:border-muted-foreground/50'
                    }`}
                >
                  <Upload size={24} className="text-muted-foreground mb-2" />
                  <span className="text-xs text-muted-foreground text-center px-2">
                    Drop end image here
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {imageSizeError && (
        <div className="text-red-500 text-xs text-center bg-red-50 dark:bg-red-900/20 p-2 rounded-lg">
          {imageSizeError}
        </div>
      )}

      {/* Upload Instructions */}
      <div className="text-xs text-muted-foreground text-center space-y-1">
        <div>Upload start and end frame images for video generation</div>
        <div>Supported formats: PNG, JPG, JPEG only</div>
        <div>Minimum size {IMAGE_CONSTRAINTS.MIN_WIDTH}×{IMAGE_CONSTRAINTS.MIN_HEIGHT} pixels, aspect ratio {IMAGE_CONSTRAINTS.MIN_ASPECT_RATIO}-{IMAGE_CONSTRAINTS.MAX_ASPECT_RATIO}</div>
        <div>Images over 10MB will be automatically compressed</div>
      </div>

      {/* Hidden File Inputs */}
      <input
        ref={startFileInputRef}
        type="file"
        accept="image/png,image/jpeg,image/jpg"
        onChange={handleStartImageUpload}
        className="hidden"
      />
      <input
        ref={endFileInputRef}
        type="file"
        accept="image/png,image/jpeg,image/jpg"
        onChange={handleEndImageUpload}
        className="hidden"
      />
    </div>
  );
};
