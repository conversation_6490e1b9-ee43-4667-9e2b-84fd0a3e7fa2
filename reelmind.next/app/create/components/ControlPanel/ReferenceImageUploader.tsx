"use client"

import React, { useMemo } from 'react';
import Image from "next/image";
import { Upload, X, Clipboard, Film } from "lucide-react";
import { useControlPanel } from './ControlPanelContext';
import { IMAGE_CONSTRAINTS } from "@/constants/upload";

export const ReferenceImageUploader: React.FC = () => {
  const {
    allowReferenceImage,
    images,
    isLoadingPresignedUrl,
    fileInputRef,
    referenceImageAreaRef,
    handleUploadClick,
    authenticatedHandleImageUpload,
    removeImage,
    modelType,
    imageSizeError
  } = useControlPanel();

  // 根据模型类型确定上传组件的文本和接受的文件类型
  const isVideoToVideo = useMemo(() => modelType === 'video-to-video', [modelType]);

  const titleText = useMemo(() => {
    if (isVideoToVideo) return "Reference Video";
    return "Reference Image";
  }, [isVideoToVideo]);

  const dropText = useMemo(() => {
    if (isVideoToVideo) return "Drop video here";
    return "Drop image here";
  }, [isVideoToVideo]);

  const acceptFileTypes = useMemo(() => {
    if (isVideoToVideo) return "video/*";
    return "image/png,image/jpeg,image/jpg";
  }, [isVideoToVideo]);

  if (!allowReferenceImage) {
    return null;
  }

  return (
    <div className="p-2.5 rounded-xl bg-card/10 border border-foreground/10">
      <div className="flex items-center mb-2">
        <div className="flex items-center">
          <h3 className="text-base font-medium">{titleText}</h3>
        </div>
        <div className="text-xs text-muted-foreground ml-auto hidden sm:flex items-center">
          <Clipboard size={12} className="mr-1" />
          <span>Ctrl+V to paste</span>
        </div>
      </div>

      <div className="flex justify-center mx-auto" ref={referenceImageAreaRef}>
        {images.length > 0 ? (
          <div className="h-[160px] aspect-square rounded-xl overflow-hidden">
            <div className="relative w-full h-full">
              {isVideoToVideo ? (
                // 视频预览
                <div className="w-full h-full flex items-center justify-center bg-black">
                  <Film size={40} className="text-white/70" />
                  <div className="absolute bottom-0 left-0 right-0 bg-black/60 text-white text-xs py-1 px-2 text-center truncate">
                    Video uploaded
                  </div>
                </div>
              ) : (
                // 图片预览
                <Image
                  src={images[0]}
                  alt="Reference image"
                  fill
                  className={`object-cover ${imageSizeError ? 'border-2 border-red-500' : ''}`}
                />
              )}
              <button
                onClick={() => removeImage(0)}
                className="absolute top-2 right-2 p-1.5 bg-black/60 hover:bg-black/80 rounded-full text-white z-10"
                aria-label={`Remove reference ${isVideoToVideo ? 'video' : 'image'}`}
              >
                <X size={14} />
              </button>
              {isLoadingPresignedUrl && (
                <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
                  <div className="text-xs text-white bg-black/50 px-2 py-1 rounded-md flex items-center">
                    <div className="mr-2 animate-spin w-3 h-3 rounded-full border-2 border-t-white border-white/30"></div>
                    Uploading...
                  </div>
                </div>
              )}
              {imageSizeError && !isVideoToVideo && (
                <div className="absolute inset-0 bg-red-500/30 flex items-center justify-center">
                  <div className="text-xs text-white bg-red-500/90 px-3 py-2 rounded-md text-center font-medium shadow-lg">
                    {imageSizeError}
                  </div>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div
            onClick={handleUploadClick}
            className="w-full h-full flex flex-col items-center justify-center bg-card/20 cursor-pointer hover:bg-card/30 transition-colors"
          >
            <Upload size={24} className="text-muted-foreground mb-2" />
            <p className="text-sm text-center">{dropText}</p>
            <p className="text-xs text-muted-foreground mt-1">
              or click to upload
            </p>
            {!isVideoToVideo && <p className="text-xs text-muted-foreground mt-1">
              PNG, JPG, JPEG only
            </p>}
            <p className="text-xs text-muted-foreground mt-1">
              Minimum {IMAGE_CONSTRAINTS.MIN_WIDTH}×{IMAGE_CONSTRAINTS.MIN_HEIGHT} pixels, aspect ratio {IMAGE_CONSTRAINTS.MIN_ASPECT_RATIO}-{IMAGE_CONSTRAINTS.MAX_ASPECT_RATIO}
            </p>
          </div>
        )}
      </div>
      <input
        ref={fileInputRef}
        type="file"
        id="media-upload"
        accept={acceptFileTypes}
        onChange={authenticatedHandleImageUpload}
        className="hidden"
        aria-label={`Upload reference ${isVideoToVideo ? 'video' : 'image'}`}
        title={`Upload reference ${isVideoToVideo ? 'video' : 'image'}`}
      />
    </div>
  );
};
