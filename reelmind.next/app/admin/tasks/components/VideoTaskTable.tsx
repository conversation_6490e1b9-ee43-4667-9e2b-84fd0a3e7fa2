"use client";

import React, { useState } from 'react';
import { Eye, Clock, CheckCircle, XCircle, AlertCircle, RefreshCw, AlertTriangle } from 'lucide-react';
import { useToast } from '@/hooks/use-sonner-toast';
import { adminTaskApi } from '@/lib/api/admin-task';

interface VideoTask {
    id: string;
    user_id: string;
    status: 'pending' | 'queued' | 'processing' | 'completed' | 'failed' | 'canceled';
    progress: number;
    input_params: any;
    output_result?: any;
    error_log?: any;
    created_at: string;
    started_at?: string;
    completed_at?: string;
    last_activity_at: string;
    handler?: string;
    retry_count?: number;
    priority?: number;
    profiles?: {
        email: string;
        full_name: string;
    };
}

interface VideoTaskTableProps {
    tasks: VideoTask[];
    loading: boolean;
    total: number;
    page: number;
    limit: number;
    onPageChange: (page: number) => void;
    onViewDetails: (taskId: string) => void;
    onRetryTask?: (taskId: string) => void;
}

// 状态显示组件
const StatusBadge: React.FC<{ status: VideoTask['status'] }> = ({ status }) => {
    const statusConfig = {
        pending: { icon: Clock, text: '等待中', className: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' },
        queued: { icon: Clock, text: '队列中', className: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300' },
        processing: { icon: AlertCircle, text: '处理中', className: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300' },
        completed: { icon: CheckCircle, text: '已完成', className: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' },
        failed: { icon: XCircle, text: '失败', className: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' },
        canceled: { icon: XCircle, text: '已取消', className: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300' }
    };

    const config = statusConfig[status];
    const Icon = config.icon;

    return (
        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.className}`}>
            <Icon className="w-3 h-3 mr-1" />
            {config.text}
        </span>
    );
};

// 模型信息显示组件
const ModelInfo: React.FC<{ inputParams: any }> = ({ inputParams }) => {
    const modelName = inputParams?.model_name || inputParams?.model_id;

    if (!modelName) {
        return <span className="text-gray-500 dark:text-gray-400">未知模型</span>;
    }

    return (
        <div className="text-sm">
            <div className="text-gray-900 dark:text-white font-medium">
                {inputParams?.model_name || '未命名'}
            </div>
            {inputParams?.model_id && inputParams?.model_name && (
                <div className="text-gray-500 dark:text-gray-400 text-xs">
                    {inputParams.model_id}
                </div>
            )}
        </div>
    );
};

// 错误日志显示组件
const ErrorLogDisplay: React.FC<{ errorLog: any }> = ({ errorLog }) => {
    if (!errorLog) return null;

    const getErrorMessage = (log: any): string => {
        if (typeof log === 'string') return log;
        if (log.message) return log.message;
        if (log.error) return log.error;
        if (log.detail) return log.detail;
        return JSON.stringify(log);
    };

    const errorMessage = getErrorMessage(errorLog);
    const truncatedMessage = errorMessage.length > 100 ? errorMessage.substring(0, 100) + '...' : errorMessage;

    return (
        <div className="flex items-start space-x-2">
            <AlertTriangle className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-red-600 dark:text-red-400">
                <div className="font-medium">Error:</div>
                <div className="text-xs text-red-500 dark:text-red-300" title={errorMessage}>
                    {truncatedMessage}
                </div>
            </div>
        </div>
    );
};

export function VideoTaskTable({
    tasks,
    loading,
    total,
    page,
    limit,
    onPageChange,
    onViewDetails,
    onRetryTask
}: VideoTaskTableProps) {
    const [retryingTasks, setRetryingTasks] = useState<Set<string>>(new Set());
    const { success, error } = useToast();

    // 重试任务
    const handleRetryTask = async (taskId: string) => {
        setRetryingTasks(prev => new Set(prev).add(taskId));

        try {
            const result = await adminTaskApi.retryTask(taskId);

            success('Task Retry Success', result.message || 'Task has been successfully retried');

            // 调用父组件的回调
            if (onRetryTask) {
                onRetryTask(taskId);
            }
        } catch (err) {
            error('Task Retry Failed', err instanceof Error ? err.message : 'Unknown error occurred');
        } finally {
            setRetryingTasks(prev => {
                const newSet = new Set(prev);
                newSet.delete(taskId);
                return newSet;
            });
        }
    };

    // 检查任务是否可以重试
    const canRetryTask = (task: VideoTask): boolean => {
        return (task.status === 'pending' || task.status === 'failed') &&
            (!task.error_log || task.error_log.retryable !== false);
    };
    const totalPages = Math.ceil(total / limit);

    if (loading) {
        return (
            <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
            </div>
        );
    }

    return (
        <div className="space-y-4">
            {/* 表格 */}
            <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-800">
                        <tr>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                                Task ID
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                                User
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                                Status
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                                Model Info
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                                Created Time
                            </th>
                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider dark:text-gray-300">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200 dark:bg-gray-900 dark:divide-gray-700">
                        {tasks.map((task) => (
                            <tr key={task.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                    {task.id.slice(0, 8)}...
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                    <div className="text-sm text-gray-900 dark:text-white">
                                        {task.profiles?.full_name || 'Unknown'}
                                    </div>
                                    <div className="text-sm text-gray-500 dark:text-gray-400">
                                        {task.profiles?.email || task.user_id.slice(0, 8) + '...'}
                                    </div>
                                </td>
                                <td className="px-6 py-4">
                                    <div className="space-y-2">
                                        <StatusBadge status={task.status} />
                                        {task.status === 'failed' && task.error_log && (
                                            <ErrorLogDisplay errorLog={task.error_log} />
                                        )}
                                    </div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                    <ModelInfo inputParams={task.input_params} />
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                    {new Date(task.created_at).toLocaleString('en-US')}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div className="flex items-center space-x-2">
                                        <button
                                            onClick={() => onViewDetails(task.id)}
                                            className="text-blue-600 hover:text-blue-500 dark:text-blue-400"
                                            title="View Details"
                                        >
                                            <Eye className="w-4 h-4" />
                                        </button>
                                        {canRetryTask(task) && (
                                            <button
                                                onClick={() => handleRetryTask(task.id)}
                                                disabled={retryingTasks.has(task.id)}
                                                className="text-green-600 hover:text-green-500 dark:text-green-400 disabled:opacity-50 disabled:cursor-not-allowed"
                                                title="Retry Task"
                                            >
                                                <RefreshCw className={`w-4 h-4 ${retryingTasks.has(task.id) ? 'animate-spin' : ''}`} />
                                            </button>
                                        )}
                                    </div>
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>

            {/* 分页 */}
            {totalPages > 1 && (
                <div className="flex items-center justify-between px-6 py-3 bg-white border-t border-gray-200 dark:bg-gray-900 dark:border-gray-700">
                    <div className="flex-1 flex justify-between sm:hidden">
                        <button
                            onClick={() => onPageChange(page - 1)}
                            disabled={page <= 1}
                            className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300"
                        >
                            Previous
                        </button>
                        <button
                            onClick={() => onPageChange(page + 1)}
                            disabled={page >= totalPages}
                            className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300"
                        >
                            Next
                        </button>
                    </div>
                    <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p className="text-sm text-gray-700 dark:text-gray-300">
                                Showing <span className="font-medium">{(page - 1) * limit + 1}</span> to{' '}
                                <span className="font-medium">{Math.min(page * limit, total)}</span> of{' '}
                                <span className="font-medium">{total}</span> results
                            </p>
                        </div>
                        <div>
                            <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                                <button
                                    onClick={() => onPageChange(page - 1)}
                                    disabled={page <= 1}
                                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300"
                                >
                                    Previous
                                </button>
                                {/* 页码按钮 */}
                                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                                    const pageNum = Math.max(1, page - 2) + i;
                                    if (pageNum > totalPages) return null;
                                    return (
                                        <button
                                            key={pageNum}
                                            onClick={() => onPageChange(pageNum)}
                                            className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${pageNum === page
                                                ? 'z-10 bg-primary-50 border-primary-500 text-primary-600 dark:bg-primary-900 dark:border-primary-400'
                                                : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300'
                                                }`}
                                        >
                                            {pageNum}
                                        </button>
                                    );
                                })}
                                <button
                                    onClick={() => onPageChange(page + 1)}
                                    disabled={page >= totalPages}
                                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300"
                                >
                                    Next
                                </button>
                            </nav>
                        </div>
                    </div>
                </div>
            )}

            {/* 空状态 */}
            {tasks.length === 0 && !loading && (
                <div className="text-center py-12">
                    <p className="text-gray-500 dark:text-gray-400">No task data available</p>
                </div>
            )}
        </div>
    );
} 