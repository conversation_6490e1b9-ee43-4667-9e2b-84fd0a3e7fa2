"use client"

import { toast } from "sonner"

/**
 * Sonner toast hook that provides a compatible API with the existing useToast hook
 */
export function useSonnerToast() {
  return {
    // Main toast function with object parameter (compatible with existing usage)
    toast: (options: {
      type: "success" | "error" | "warning" | "info"
      title: string
      message?: string
      duration?: number
    }) => {
      const { type, title, message, duration } = options
      const description = message
      
      switch (type) {
        case "success":
          return toast.success(title, { description, duration })
        case "error":
          return toast.error(title, { description, duration })
        case "warning":
          return toast.warning(title, { description, duration })
        case "info":
          return toast.info(title, { description, duration })
        default:
          return toast(title, { description, duration })
      }
    },

    // Convenience methods (compatible with existing usage)
    success: (title: string, message?: string, duration?: number) =>
      toast.success(title, { description: message, duration }),
    
    error: (title: string, message?: string, duration?: number) =>
      toast.error(title, { description: message, duration }),
    
    warning: (title: string, message?: string, duration?: number) =>
      toast.warning(title, { description: message, duration }),
    
    info: (title: string, message?: string, duration?: number) =>
      toast.info(title, { description: message, duration }),

    // Direct access to sonner toast for advanced usage
    sonner: toast,
  }
}

// Export a simplified version that matches the existing useToast API exactly
export function useToast() {
  return useSonnerToast()
}
