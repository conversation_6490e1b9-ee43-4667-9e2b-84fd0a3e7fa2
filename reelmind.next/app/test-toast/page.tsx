"use client"

import React from 'react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-sonner-toast';
import { useTheme } from 'next-themes';
import { Moon, Sun } from 'lucide-react';

export default function TestToastPage() {
  const { success, error, warning, info, toast } = useToast();
  const { theme, setTheme } = useTheme();

  const handleSuccessToast = () => {
    success("Success!", "This is a success message");
  };

  const handleErrorToast = () => {
    error("Error!", "This is an error message");
  };

  const handleWarningToast = () => {
    warning("Warning!", "This is a warning message");
  };

  const handleInfoToast = () => {
    info("Info!", "This is an info message");
  };

  const handleObjectToast = () => {
    toast({
      type: "success",
      title: "Object Toast",
      message: "This toast was created using the object syntax",
      duration: 3000
    });
  };

  const toggleTheme = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark');
  };

  return (
    <div className="container mx-auto p-8 max-w-2xl">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Toast Test Page</h1>
        <Button onClick={toggleTheme} variant="outline" size="sm">
          {theme === 'dark' ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
          <span className="ml-2">
            {theme === 'dark' ? 'Light Mode' : 'Dark Mode'}
          </span>
        </Button>
      </div>

      <div className="space-y-4">
        <div>
          <h2 className="text-lg font-semibold mb-4">Test Sonner Toast (Top Center Position)</h2>
          <div className="grid grid-cols-2 gap-4">
            <Button onClick={handleSuccessToast} variant="default">
              Success Toast
            </Button>

            <Button onClick={handleErrorToast} variant="destructive">
              Error Toast
            </Button>

            <Button onClick={handleWarningToast} variant="outline">
              Warning Toast
            </Button>

            <Button onClick={handleInfoToast} variant="secondary">
              Info Toast
            </Button>

            <Button onClick={handleObjectToast} variant="outline" className="col-span-2">
              Object Syntax Toast
            </Button>
          </div>
        </div>

        <div className="mt-8 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">
          <h3 className="font-semibold mb-2">Test Instructions:</h3>
          <ul className="text-sm space-y-1">
            <li>• Click each button to test different toast types</li>
            <li>• Toasts should appear at the top center of the screen</li>
            <li>• Use the theme toggle button to switch between light/dark modes</li>
            <li>• <strong>Light Mode:</strong> Toasts should have black background with white text</li>
            <li>• <strong>Dark Mode:</strong> Toasts should have white background with black text</li>
            <li>• They should automatically disappear after a few seconds</li>
            <li>• You can manually close them by clicking the X button</li>
          </ul>
        </div>

        <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <h3 className="font-semibold mb-2">Migration Status:</h3>
          <ul className="text-sm space-y-1">
            <li>✅ Replaced ToastProvider with Sonner Toaster</li>
            <li>✅ Updated all useToast imports to use Sonner</li>
            <li>✅ Positioned toasts at top-center</li>
            <li>✅ Maintained API compatibility</li>
            <li>✅ Applied custom color scheme:</li>
            <li className="ml-4">• Light mode: Black background, white text</li>
            <li className="ml-4">• Dark mode: White background, black text</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
