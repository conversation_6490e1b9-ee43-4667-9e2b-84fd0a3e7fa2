# Start-to-End Image Upload Bug Fix

## 问题描述

发现了一个严重的bug：对于start-to-end模型的图片上传，没有限制等待图片上传完毕才能提交任务。用户可以在图片还没上传完成时就提交任务，导致后台任务失败，因为取不到图片URL。

## 根本原因分析

1. **异步上传问题**：图片上传是异步的，先显示本地预览，然后异步上传到服务器
2. **验证逻辑不完善**：只检查图片是否以`data:`开头，但不够准确
3. **状态管理缺失**：没有区分本地预览和服务器上传完成状态
4. **按钮禁用逻辑缺失**：GenerateButton没有考虑图片上传状态

## 修复方案

### 1. 改进状态管理

在`useStartToEndModel` hook中添加了更细粒度的状态：

```typescript
export interface StartToEndModelHook {
  // 新增状态
  isStartImageUploading: boolean;
  isEndImageUploading: boolean;
  // 原有状态
  isUploading: boolean; // 任一图片上传中
}
```

### 2. 增强验证逻辑

改进`validateStartToEndModel`方法：

```typescript
const validateStartToEndModel = useCallback(() => {
  // 检查图片是否存在
  if (startImages.length === 0) {
    return { isValid: false, errorMessage: "Please upload a start frame image" };
  }
  if (endImages.length === 0) {
    return { isValid: false, errorMessage: "Please upload an end frame image" };
  }

  // 检查是否正在上传
  if (isStartImageUploading || isEndImageUploading) {
    return { isValid: false, errorMessage: "Images are uploading, please wait for upload to complete" };
  }

  // 检查是否还是本地预览
  if (startImages[0].startsWith('data:') || endImages[0].startsWith('data:')) {
    return { isValid: false, errorMessage: "Images are processing, please wait for upload to complete" };
  }

  return { isValid: true };
}, [startImages, endImages, isStartImageUploading, isEndImageUploading]);
```

### 3. 更新按钮禁用逻辑

在`GenerateButton`组件中添加上传状态检查：

```typescript
const isDisabled = hasInsufficientCredits
  ? false
  : (
    isGenerating ||
    !selectedModelId ||
    isLoadingModels ||
    isLoadingPresignedUrl ||
    (isStartToEndModel && (
      startImages.length === 0 || 
      endImages.length === 0 || 
      isStartImageUploading ||  // 新增
      isEndImageUploading ||    // 新增
      startImages[0]?.startsWith('data:') ||  // 新增
      endImages[0]?.startsWith('data:')       // 新增
    )) ||
    // ... 其他条件
  );
```

### 4. 用户体验改进

在`StartToEndImageUploader`组件中添加：

- **上传状态指示器**：显示旋转加载图标和"Uploading..."文本
- **禁用交互**：上传期间禁用点击和删除操作
- **视觉反馈**：上传区域显示半透明遮罩

## 修复的文件

1. `hooks/use-start-to-end-model.ts` - 核心状态管理和验证逻辑
2. `app/create/components/ControlPanel/ControlPanelContext.tsx` - 暴露新状态
3. `app/create/components/ControlPanel/GenerateButton.tsx` - 按钮禁用逻辑
4. `app/create/components/ControlPanel/StartToEndImageUploader.tsx` - UI状态指示器

## 测试验证

### 测试场景

1. **正常流程**：
   - 选择start-to-end模型
   - 上传start图片，等待完成
   - 上传end图片，等待完成
   - 确认Generate按钮可用
   - 提交任务成功

2. **上传中断测试**：
   - 上传图片过程中，Generate按钮应该被禁用
   - 显示"Uploading Images..."状态
   - 无法点击删除按钮

3. **错误处理**：
   - 上传失败时清除预览
   - 显示适当的错误消息

### 预期结果

- ✅ 用户无法在图片上传完成前提交任务
- ✅ 清晰的视觉反馈显示上传状态
- ✅ 后台任务不再因为图片URL缺失而失败
- ✅ 用户体验得到改善

## 部署注意事项

1. 这是一个关键的bug修复，建议优先部署
2. 修复向后兼容，不影响其他模型类型
3. 建议部署后监控start-to-end任务的成功率

## 后续优化建议

1. 考虑添加上传进度条
2. 实现图片上传重试机制
3. 添加更详细的错误日志记录
