"use client";

import React, { useState, useEffect } from 'react';
import { useToast } from '@/hooks/use-sonner-toast';
import { VideoTaskTable } from './VideoTaskTable';
import { VideoTaskFilters } from './VideoTaskFilters';
import { VideoTaskDetails } from './VideoTaskDetails';

interface VideoTask {
    id: string;
    user_id: string;
    status: 'pending' | 'queued' | 'processing' | 'completed' | 'failed' | 'canceled';
    progress: number;
    input_params: any;
    output_result?: any;
    error_log?: any;
    created_at: string;
    started_at?: string;
    completed_at?: string;
    last_activity_at: string;
    handler?: string;
    retry_count?: number;
    priority?: number;
    profiles?: {
        email: string;
        full_name: string;
    };
}

interface VideoTasksResponse {
    tasks: VideoTask[];
    total: number;
    page: number;
    limit: number;
    error?: string;
}

interface Filters {
    status?: string;
    userId?: string;
    handler?: string;
}

export function VideoTasksManagement() {
    const [tasks, setTasks] = useState<VideoTask[]>([]);
    const [loading, setLoading] = useState(true);
    const [total, setTotal] = useState(0);
    const [page, setPage] = useState(1);
    const [limit] = useState(20);
    const [filters, setFilters] = useState<Filters>({});
    const [selectedTaskId, setSelectedTaskId] = useState<string | null>(null);
    const { error } = useToast();

    // 获取任务列表
    const fetchTasks = async () => {
        try {
            setLoading(true);
            const params = new URLSearchParams({
                page: page.toString(),
                limit: limit.toString(),
                ...filters
            });

            const response = await fetch(`/api/admin/video-tasks?${params}`);
            const data: VideoTasksResponse = await response.json();

            if (!response.ok) {
                throw new Error(data.error || '获取任务列表失败');
            }

            setTasks(data.tasks);
            setTotal(data.total);
        } catch (err) {
            error('获取任务列表失败', err instanceof Error ? err.message : '未知错误');
        } finally {
            setLoading(false);
        }
    };

    // 处理过滤器变化
    const handleFiltersChange = (newFilters: Filters) => {
        setFilters(newFilters);
        setPage(1); // 重置到第一页
    };

    // 初始加载和依赖更新
    useEffect(() => {
        fetchTasks();
    }, [page, filters]);

    return (
        <div className="p-6 space-y-6">
            {/* 过滤器 */}
            <VideoTaskFilters
                filters={filters}
                onFiltersChange={handleFiltersChange}
                onRefresh={fetchTasks}
            />

            {/* 任务表格 */}
            <VideoTaskTable
                tasks={tasks}
                loading={loading}
                total={total}
                page={page}
                limit={limit}
                onPageChange={setPage}
                onViewDetails={setSelectedTaskId}
                onRetryTask={() => fetchTasks()} // 重试后刷新任务列表
            />

            {/* 任务详情模态框 */}
            {selectedTaskId && (
                <VideoTaskDetails
                    taskId={selectedTaskId}
                    open={!!selectedTaskId}
                    onClose={() => setSelectedTaskId(null)}
                />
            )}
        </div>
    );
} 