"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { postApi } from "@/lib/api"
import { Progress } from "@/components/ui/progress"
import { useToast } from "@/hooks/use-sonner-toast"
import type { VideoTask } from "@/types/video-task"
import type { ChangeEvent } from "react"
import { Share, Image, PenLine, Heart, Users, Sparkles, CheckCircle2 } from "lucide-react"
import { useTheme } from "next-themes"
import { cn } from "@/lib/utils"

interface PublishDialogProps {
    open: boolean
    onOpenChange: (open: boolean) => void
    task: VideoTask
    onSuccess?: () => void
}

export function PublishDialog({ open, onOpenChange, task, onSuccess }: PublishDialogProps) {
    const [title, setTitle] = useState("")
    const [description, setDescription] = useState("")
    const [isPublishing, setIsPublishing] = useState(false)
    const [publishProgress, setPublishProgress] = useState(0)
    const { theme } = useTheme()
    const isDarkTheme = theme === 'dark'
    const toast = useToast()

    // Reset form when dialog opens
    useEffect(() => {
        if (open) {
            // Extract a title from the prompt as initial value
            if (task.input_params.prompt) {
                // Limit to first 30 characters
                setTitle(task.input_params.prompt.slice(0, 30))
            }
        }
    }, [open, task]);

    // Simulate progress during publishing
    useEffect(() => {
        if (isPublishing) {
            const interval = setInterval(() => {
                setPublishProgress(prev => {
                    if (prev >= 90) {
                        clearInterval(interval)
                        return prev
                    }
                    return prev + 10
                })
            }, 300)

            return () => clearInterval(interval)
        } else {
            setPublishProgress(0)
        }
    }, [isPublishing])

    // Handle publishing to community
    const handlePublish = async () => {
        if (!title.trim()) {
            toast.error("Publication Failed", "Please enter a title for your video");
            return;
        }

        try {
            setIsPublishing(true);

            // Show processing toast
            toast.info("Processing", "Preparing to publish your video...");

            // Prepare publish parameters
            const publishParams = {
                taskId: task.id,
                title: title.trim(),
                description: description.trim()
            };

            // Send request
            const response = await postApi.createPost(publishParams);

            // Complete progress
            setPublishProgress(100);

            // Show success toast
            toast.success(
                "Successfully Published",
                "Your video has been shared to the community"
            );

            // Reset form and close dialog after delay
            setTimeout(() => {
                setTitle("");
                setDescription("");
                setIsPublishing(false);

                // Close dialog
                onOpenChange(false);

                // Call success callback
                onSuccess?.();
            }, 1000);

        } catch (error) {
            console.error("Publish failed:", error);
            toast.error("Publication Failed", "Please check your network connection or try again later");
            setIsPublishing(false);
        }
    }

    // Render publishing state
    const renderPublishingState = () => (
        <div className="flex flex-col items-center justify-center py-8">
            {publishProgress === 100 ? (
                <>
                    {/* Success state */}
                    <div className={cn(
                        "w-20 h-20 rounded-full flex items-center justify-center mb-6 relative",
                        isDarkTheme
                            ? "bg-gradient-to-r from-green-500 to-emerald-600"
                            : "bg-gradient-to-r from-green-400 to-emerald-500"
                    )}>
                        <CheckCircle2 className="h-10 w-10 text-white" />
                        <div className="absolute inset-0 rounded-full bg-gradient-to-r from-green-400/20 to-emerald-500/20 animate-pulse"></div>
                    </div>
                    <h3 className={cn(
                        "text-xl font-semibold mb-3",
                        isDarkTheme ? "text-white" : "text-gray-900"
                    )}>
                        🎉 Successfully Published!
                    </h3>
                    <p className={cn(
                        "text-center max-w-xs leading-relaxed",
                        isDarkTheme ? "text-gray-300" : "text-gray-600"
                    )}>
                        Your amazing creation is now live in the community
                    </p>
                </>
            ) : (
                <>
                    {/* Loading state */}
                    <div className="relative w-20 h-20 mb-6">
                        <div className={cn(
                            "absolute inset-0 rounded-full",
                            isDarkTheme
                                ? "bg-gradient-to-r from-blue-500/20 to-purple-600/20"
                                : "bg-gradient-to-r from-blue-400/20 to-purple-500/20"
                        )}></div>
                        <div className={cn(
                            "absolute inset-0 rounded-full border-4 border-transparent animate-spin",
                            isDarkTheme
                                ? "border-t-blue-500 border-r-purple-600"
                                : "border-t-blue-400 border-r-purple-500"
                        )}></div>
                        <div className="absolute inset-0 flex items-center justify-center">
                            <Sparkles className={cn(
                                "h-8 w-8 animate-pulse",
                                isDarkTheme ? "text-blue-400" : "text-blue-500"
                            )} />
                        </div>
                    </div>
                    <h3 className={cn(
                        "text-xl font-semibold mb-3",
                        isDarkTheme ? "text-white" : "text-gray-900"
                    )}>
                        ✨ Publishing Your Masterpiece
                    </h3>
                    <p className={cn(
                        "text-center max-w-xs mb-6 leading-relaxed",
                        isDarkTheme ? "text-gray-300" : "text-gray-600"
                    )}>
                        We're preparing your video for the community to enjoy...
                    </p>

                    {/* Enhanced Progress bar */}
                    <div className="w-full max-w-sm mb-3">
                        <div className={cn(
                            "w-full h-2 rounded-full overflow-hidden",
                            isDarkTheme ? "bg-gray-700" : "bg-gray-200"
                        )}>
                            <div
                                className={cn(
                                    "h-full rounded-full transition-all duration-500 ease-out",
                                    isDarkTheme
                                        ? "bg-gradient-to-r from-blue-500 to-purple-600"
                                        : "bg-gradient-to-r from-blue-400 to-purple-500"
                                )}
                                style={{ width: `${publishProgress}%` }}
                            >
                                <div className="w-full h-full bg-gradient-to-r from-white/20 to-transparent animate-pulse"></div>
                            </div>
                        </div>
                    </div>
                    <p className={cn(
                        "text-sm font-medium",
                        isDarkTheme ? "text-blue-400" : "text-blue-600"
                    )}>
                        {publishProgress}% Complete
                    </p>
                </>
            )}
        </div>
    );

    return (
        <Dialog open={open} onOpenChange={(newOpen) => !isPublishing && onOpenChange(newOpen)}>
            <DialogContent className={cn(
                "w-[90vw] max-w-md max-h-[85vh] overflow-hidden border-0 p-0 rounded-2xl",
                "flex flex-col",
                isDarkTheme
                    ? "bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900"
                    : "bg-gradient-to-br from-white via-gray-50 to-white"
            )}>
                {/* Header with gradient background */}
                <div className={cn(
                    "relative px-4 py-4 pb-3 flex-shrink-0",
                    isDarkTheme
                        ? "bg-gradient-to-r from-blue-600/20 via-purple-600/20 to-pink-600/20"
                        : "bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10"
                )}>
                    {/* Decorative elements */}
                    <div className="absolute top-0 left-0 w-full h-full overflow-hidden rounded-t-2xl">
                        <div className={cn(
                            "absolute -top-4 -right-4 w-16 h-16 rounded-full opacity-20",
                            isDarkTheme ? "bg-blue-500" : "bg-blue-400"
                        )}></div>
                        <div className={cn(
                            "absolute -bottom-6 -left-6 w-20 h-20 rounded-full opacity-10",
                            isDarkTheme ? "bg-purple-600" : "bg-purple-500"
                        )}></div>
                    </div>

                    <DialogHeader className="relative z-10">
                        <DialogTitle className={cn(
                            "flex items-center text-lg font-bold mb-1",
                            isDarkTheme ? "text-white" : "text-gray-900"
                        )}>
                            <div className={cn(
                                "mr-2 p-1.5 rounded-lg",
                                isDarkTheme
                                    ? "bg-gradient-to-r from-blue-500 to-purple-600"
                                    : "bg-gradient-to-r from-blue-400 to-purple-500"
                            )}>
                                <Share className="h-4 w-4 text-white" />
                            </div>
                            Share to Community
                        </DialogTitle>
                        <DialogDescription className={cn(
                            "text-sm leading-relaxed",
                            isDarkTheme ? "text-gray-300" : "text-gray-600"
                        )}>
                            ✨ Share your amazing creation with the world
                        </DialogDescription>
                    </DialogHeader>
                </div>

                {/* Content - scrollable if needed */}
                <div className="flex-1 overflow-y-auto px-4 pb-4">
                    {/* Publishing state or form content */}
                    {isPublishing ? (
                        renderPublishingState()
                    ) : (
                        <div className="space-y-4">
                            {/* Enhanced Benefits section */}
                            <div className={cn(
                                "flex items-center justify-center gap-4 p-3 rounded-xl text-xs",
                                isDarkTheme
                                    ? "bg-gradient-to-r from-gray-800/50 to-gray-700/50 border border-gray-600/20"
                                    : "bg-gradient-to-r from-gray-50 to-white border border-gray-200/50"
                            )}>
                                <div className="flex items-center gap-1">
                                    <Heart className={cn(
                                        "h-3 w-3",
                                        isDarkTheme ? "text-red-400" : "text-red-500"
                                    )} />
                                    <span className={cn(
                                        "font-medium",
                                        isDarkTheme ? "text-gray-300" : "text-gray-700"
                                    )}>
                                        Get Likes
                                    </span>
                                </div>
                                <div className="flex items-center gap-1">
                                    <Users className={cn(
                                        "h-3 w-3",
                                        isDarkTheme ? "text-blue-400" : "text-blue-500"
                                    )} />
                                    <span className={cn(
                                        "font-medium",
                                        isDarkTheme ? "text-gray-300" : "text-gray-700"
                                    )}>
                                        Build Community
                                    </span>
                                </div>
                                <div className="flex items-center gap-1">
                                    <Sparkles className={cn(
                                        "h-3 w-3",
                                        isDarkTheme ? "text-purple-400" : "text-purple-500"
                                    )} />
                                    <span className={cn(
                                        "font-medium",
                                        isDarkTheme ? "text-gray-300" : "text-gray-700"
                                    )}>
                                        Inspire Others
                                    </span>
                                </div>
                            </div>

                            {/* Enhanced Video Preview */}
                            <div className={cn(
                                "aspect-video rounded-xl overflow-hidden border-2",
                                isDarkTheme
                                    ? "bg-gray-800 border-gray-700/50"
                                    : "bg-gray-100 border-gray-200/50"
                            )}>
                                {task.output_result?.video_url ? (
                                    <video
                                        src={task.output_result.video_url}
                                        className="w-full h-full object-cover"
                                        controls
                                        autoPlay
                                        loop
                                        muted
                                    />
                                ) : (
                                    <div className="w-full h-full flex items-center justify-center">
                                        <div className="text-center">
                                            <Image className={cn(
                                                "h-8 w-8 mx-auto mb-2",
                                                isDarkTheme ? "text-gray-600" : "text-gray-400"
                                            )} />
                                            <p className={cn(
                                                "text-xs",
                                                isDarkTheme ? "text-gray-400" : "text-gray-500"
                                            )}>
                                                Video preview unavailable
                                            </p>
                                        </div>
                                    </div>
                                )}
                            </div>

                            {/* Enhanced Title Input */}
                            <div className="space-y-2">
                                <Label htmlFor="title" className={cn(
                                    "text-sm font-semibold flex items-center",
                                    isDarkTheme ? "text-gray-200" : "text-gray-800"
                                )}>
                                    <div className={cn(
                                        "mr-2 p-1 rounded-md",
                                        isDarkTheme ? "bg-blue-500/20" : "bg-blue-500/10"
                                    )}>
                                        <PenLine className={cn(
                                            "h-3 w-3",
                                            isDarkTheme ? "text-blue-400" : "text-blue-600"
                                        )} />
                                    </div>
                                    Video Title <span className="text-red-500 ml-1">*</span>
                                </Label>
                                <Input
                                    id="title"
                                    value={title}
                                    onChange={(e: ChangeEvent<HTMLInputElement>) => setTitle(e.target.value)}
                                    placeholder="Give your creation an engaging title..."
                                    className={cn(
                                        "h-10 text-sm transition-all duration-200 border-2",
                                        isDarkTheme
                                            ? "bg-gray-800/50 border-gray-700 focus:border-blue-500 focus:ring-blue-500/20"
                                            : "bg-white border-gray-300 focus:border-blue-500 focus:ring-blue-500/20"
                                    )}
                                    disabled={isPublishing}
                                    maxLength={100}
                                />
                                <p className={cn(
                                    "text-xs flex justify-between",
                                    isDarkTheme ? "text-gray-400" : "text-gray-500"
                                )}>
                                    <span>A compelling title attracts more viewers</span>
                                    <span className={cn(
                                        title.length > 80 ? "text-orange-500" : "text-current"
                                    )}>
                                        {title.length}/100
                                    </span>
                                </p>
                            </div>

                            {/* Enhanced Description Input */}
                            <div className="space-y-2">
                                <Label htmlFor="description" className={cn(
                                    "text-sm font-semibold flex items-center",
                                    isDarkTheme ? "text-gray-200" : "text-gray-800"
                                )}>
                                    <div className={cn(
                                        "mr-2 p-1 rounded-md",
                                        isDarkTheme ? "bg-purple-500/20" : "bg-purple-500/10"
                                    )}>
                                        <PenLine className={cn(
                                            "h-3 w-3",
                                            isDarkTheme ? "text-purple-400" : "text-purple-600"
                                        )} />
                                    </div>
                                    Description
                                    <span className={cn(
                                        "ml-2 text-xs px-1.5 py-0.5 rounded-full",
                                        isDarkTheme ? "bg-gray-700 text-gray-300" : "bg-gray-200 text-gray-600"
                                    )}>
                                        Optional
                                    </span>
                                </Label>
                                <Textarea
                                    id="description"
                                    value={description}
                                    onChange={(e: ChangeEvent<HTMLTextAreaElement>) => setDescription(e.target.value)}
                                    placeholder="Tell the community about your creation..."
                                    rows={3}
                                    className={cn(
                                        "resize-none text-sm transition-all duration-200 border-2",
                                        isDarkTheme
                                            ? "bg-gray-800/50 border-gray-700 focus:border-purple-500 focus:ring-purple-500/20"
                                            : "bg-white border-gray-300 focus:border-purple-500 focus:ring-purple-500/20"
                                    )}
                                    disabled={isPublishing}
                                    maxLength={200}
                                />
                                <p className={cn(
                                    "text-xs flex justify-between",
                                    isDarkTheme ? "text-gray-400" : "text-gray-500"
                                )}>
                                    <span>Share your creative process</span>
                                    <span className={cn(
                                        description.length > 160 ? "text-orange-500" : "text-current"
                                    )}>
                                        {description.length}/200
                                    </span>
                                </p>
                            </div>

                            {/* Enhanced Action Buttons */}
                            <div className="flex gap-2 pt-2">
                                <Button
                                    variant="outline"
                                    onClick={() => onOpenChange(false)}
                                    disabled={isPublishing}
                                    className={cn(
                                        "flex-1 h-10 text-sm transition-all duration-200",
                                        isDarkTheme
                                            ? "border-gray-600 hover:bg-gray-700 text-gray-300"
                                            : "border-gray-300 hover:bg-gray-50 text-gray-700"
                                    )}
                                >
                                    Cancel
                                </Button>

                                <Button
                                    onClick={handlePublish}
                                    disabled={isPublishing || !title.trim()}
                                    className={cn(
                                        "flex-1 h-10 text-sm font-semibold transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]",
                                        "bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700",
                                        "shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                                    )}
                                >
                                    <Share className="h-3 w-3 mr-1" />
                                    🚀 Publish
                                </Button>
                            </div>
                        </div>
                    )}
                </div>
            </DialogContent>
        </Dialog>
    )
} 